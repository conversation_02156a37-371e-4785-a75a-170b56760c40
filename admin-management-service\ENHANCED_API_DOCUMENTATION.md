# Enhanced Admin Management Service API Documentation

## Overview

The Enhanced Admin Management Service provides comprehensive administrative capabilities for managing all aspects of the Build Connect platform. This service enables administrators to:

- **Manage Verification Requests**: Approve/reject contractor, broker, and site verifications
- **User Management**: Manage users, contractors, and brokers across the platform
- **Site Management**: Oversee site listings and their approval status
- **Analytics Dashboard**: View comprehensive analytics from all services
- **Service Management**: Manage transactions, ratings, notifications, and support tickets

## Base URL
```
http://localhost:3001/admin-service/api/v1
```

## Authentication

All endpoints require JWT authentication with session management:

**Headers Required:**
```
Authorization: Bearer <jwt_token>
Session: <session_id>
Content-Type: application/json
```

## API Endpoints

### 🏠 Dashboard & Analytics

#### GET `/`
Get admin dashboard with verification statistics and recent activity.

**Response:**
```json
{
  "totalVerificationRequests": 25,
  "pendingRequests": 8,
  "approvedRequests": 15,
  "rejectedRequests": 2,
  "contractorRequests": 12,
  "brokerRequests": 8,
  "siteRequests": 5,
  "highPriorityRequests": 3,
  "recentActivity": [
    {
      "type": "contractor",
      "status": "pending",
      "createdAt": "2025-08-07T10:30:00Z",
      "requesterId": "user123",
      "priority": "medium"
    }
  ]
}
```

#### GET `/analytics`
Get comprehensive analytics from all services.

**Query Parameters:**
- `timeframe` (optional): `7d`, `30d`, `90d`, `all` (default: `30d`)

**Response:**
```json
{
  "timeframe": "30d",
  "overview": {
    "totalUsers": 1250,
    "totalSites": 340,
    "totalTransactions": 89,
    "pendingVerifications": 8,
    "averageRating": 4.2,
    "activeSupports": 5
  },
  "verifications": {
    "total": 25,
    "pending": 8,
    "approved": 15,
    "rejected": 2,
    "approvalRate": 88.24
  },
  "users": {
    "totalUsers": 1250,
    "activeUsers": 1180,
    "newUsers": 45,
    "contractors": 320,
    "brokers": 180,
    "verifiedUsers": 890
  },
  "serviceStatus": {
    "userService": true,
    "siteService": true,
    "paymentService": false,
    "ratingService": true,
    "notificationService": true,
    "supportService": true
  }
}
```

### ✅ Verification Management

#### GET `/verifications`
Get paginated list of verification requests with filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `status` (optional): `pending`, `approved`, `rejected`
- `type` (optional): `contractor`, `broker`, `site`
- `search` (optional): Search term
- `sortBy` (optional): `createdAt`, `updatedAt`, `priority`
- `order` (optional): `asc`, `desc`
- `timeframe` (optional): `7d`, `30d`, `90d`, `all`

#### GET `/verifications/:id`
Get specific verification request details.

#### PATCH `/verifications/:id/approve`
Approve a verification request.

**Request Body:**
```json
{
  "reasonForApproval": "All documents verified and meet requirements",
  "notes": "Contractor has excellent credentials and portfolio"
}
```

#### PATCH `/verifications/:id/reject`
Reject a verification request.

**Request Body:**
```json
{
  "reasonForRejection": "Incomplete documentation - missing PAN card verification",
  "notes": "Please resubmit with complete documentation"
}
```

#### PATCH `/verifications/:id/priority`
Update verification request priority.

**Request Body:**
```json
{
  "priority": "high"
}
```

#### PATCH `/verifications/:id/notes`
Add notes to verification request.

**Request Body:**
```json
{
  "notes": "Additional verification required for business license"
}
```

#### GET `/verifications/stats`
Get verification statistics.

**Query Parameters:**
- `timeframe` (optional): `7d`, `30d`, `90d`, `all`

### 👥 User Management

#### GET `/users`
Get paginated list of users with filtering.

**Query Parameters:**
- `page`, `limit`, `search`, `sortBy`, `order` (same as verifications)
- `role` (optional): `user`, `contractor`, `broker`
- `status` (optional): User status filter

#### GET `/users/:id`
Get specific user details.

#### PATCH `/users/:id/status`
Update user status (activate/deactivate).

**Request Body:**
```json
{
  "isActive": false,
  "reason": "Suspended due to policy violation"
}
```

#### GET `/users/stats`
Get user statistics.

#### GET `/contractors`
Get paginated list of contractors with verification status.

**Query Parameters:**
- `verificationStatus` (optional): `pending`, `verified`, `rejected`
- Other standard pagination parameters

#### GET `/brokers`
Get paginated list of brokers with verification status.

### 🏢 Site Management

#### GET `/sites`
Get paginated list of sites with filtering.

**Query Parameters:**
- Standard pagination parameters
- `status` (optional): `pending`, `approved`, `rejected`
- `state` (optional): Filter by state
- `district` (optional): Filter by district

#### GET `/sites/:id`
Get specific site details.

#### PATCH `/sites/:id/status`
Update site status (approve/reject).

**Request Body:**
```json
{
  "status": "approved",
  "reason": "All documents verified and site meets requirements"
}
```

#### GET `/sites/stats`
Get site statistics.

#### GET `/sites/location`
Get sites by location.

**Query Parameters:**
- `state` (required): State name
- `district` (optional): District name
- `limit` (optional): Max results (default: 50)

#### GET `/projects`
Get project requirements for admin review.

#### GET `/service-requests`
Get service requests for admin monitoring.

### 💰 Service Management

#### GET `/transactions`
Get paginated list of transactions.

**Query Parameters:**
- Standard pagination parameters
- `status` (optional): Transaction status
- `userId` (optional): Filter by user ID

#### GET `/transactions/:id`
Get specific transaction details.

#### GET `/ratings`
Get paginated list of ratings.

**Query Parameters:**
- Standard pagination parameters
- `rating` (optional): Filter by rating value
- `entityType` (optional): Filter by entity type

#### DELETE `/ratings/:id`
Delete inappropriate rating.

**Request Body:**
```json
{
  "reason": "Inappropriate content or spam"
}
```

#### GET `/notifications`
Get paginated list of notifications.

#### POST `/notifications/broadcast`
Send broadcast notification to users.

**Request Body:**
```json
{
  "title": "System Maintenance Notice",
  "message": "The system will be under maintenance from 2 AM to 4 AM tomorrow.",
  "type": "maintenance",
  "targetAudience": "all"
}
```

#### GET `/support/tickets`
Get paginated list of support tickets.

**Query Parameters:**
- Standard pagination parameters
- `status` (optional): `open`, `in_progress`, `resolved`, `closed`
- `priority` (optional): `low`, `medium`, `high`, `urgent`
- `category` (optional): `technical`, `billing`, `general`, `feature_request`

#### PATCH `/support/tickets/:id/status`
Update support ticket status.

**Request Body:**
```json
{
  "status": "in_progress",
  "assignedTo": "admin_user_id",
  "notes": "Investigating the reported issue"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "message": "Error description"
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing authentication)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `500`: Internal Server Error
- `503`: Service Unavailable (when dependent services are down)

## Validation Rules

### Common Validations:
- **ObjectId**: Must be valid MongoDB ObjectId (24 hex characters)
- **Page**: Positive integer, minimum 1
- **Limit**: Integer between 1 and 100
- **Search**: Maximum 100 characters
- **Timeframe**: Must be `7d`, `30d`, `90d`, or `all`

### Verification Specific:
- **Priority**: Must be `low`, `medium`, or `high`
- **Status**: Must be `pending`, `approved`, or `rejected`
- **Type**: Must be `contractor`, `broker`, or `site`
- **Reason for Rejection**: Required, 10-500 characters
- **Notes**: 5-1000 characters when provided

### Notification Specific:
- **Title**: Required, 5-100 characters
- **Message**: Required, 10-500 characters
- **Type**: Must be `general`, `urgent`, `maintenance`, or `promotion`
- **Target Audience**: Must be `all`, `users`, `contractors`, or `brokers`

## Rate Limiting

- **General endpoints**: 100 requests per minute per IP
- **Analytics endpoints**: 20 requests per minute per IP
- **Broadcast notifications**: 5 requests per minute per admin

## Service Dependencies

The admin service communicates with:
- **User Management Service** (port 3007): User and professional data
- **Site Management Service** (port 3006): Site and project data
- **Payment Management Service** (port 3002): Transaction data
- **Rating Management Service** (port 3003): Rating data
- **Notification Management Service** (port 3004): Notification data
- **Customer Support Service** (port 3005): Support ticket data

When dependent services are unavailable, the admin service returns fallback responses with appropriate status messages.
