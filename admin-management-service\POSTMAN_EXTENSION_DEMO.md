# 🎯 Postman Extension Demo - Admin Management Service

## 🚀 Ready to Test! All Setup Complete

### ✅ **Current Status:**
- **Admin Service:** ✅ Running on port 3001
- **User Service:** ✅ Running on port 3007
- **Authentication:** ✅ Fresh tokens generated
- **Postman Files:** ✅ Updated with latest tokens
- **Authentication Fix:** ✅ Applied and working

## 📱 VS Code Postman Extension Usage

### **Step 1: Open Postman Extension**
1. **In VS Code:** Click the Postman icon in the sidebar (or Ctrl+Shift+P → "Postman: Focus on Postman View")
2. **Sign in** to your Postman account if prompted

### **Step 2: Import Collections**
1. **Click "Import"** in the Postman panel
2. **Select File** and choose:
   - `admin-management-service/postman-collection-complete.json`
   - `admin-management-service/postman-environment.json`
3. **Select Environment:** "Admin Management Service Environment"

### **Step 3: Test Individual Requests**

#### **🏠 Dashboard Requests (GET)**
```
1. Get Admin Dashboard
   Method: GET
   URL: {{baseUrl}}/
   Expected: 200 OK with dashboard statistics

2. Get Dashboard Analytics  
   Method: GET
   URL: {{baseUrl}}/analytics
   Expected: 200 OK with analytics data
```

#### **🔍 Verification Requests (GET, PATCH)**
```
3. Get Verification Requests
   Method: GET
   URL: {{baseUrl}}/verifications?page=1&limit=10
   Expected: 200 OK with verification list

4. Get Verification Statistics
   Method: GET
   URL: {{baseUrl}}/verifications/stats?timeframe=30d
   Expected: 200 OK with statistics

5. Approve Verification (PATCH)
   Method: PATCH
   URL: {{baseUrl}}/verifications/{{verification_id}}/approve
   Body: {
     "reasonForApproval": "All documents verified",
     "adminId": "{{admin_id}}"
   }
   Expected: 200 OK or 404 Not Found

6. Reject Verification (PATCH)
   Method: PATCH
   URL: {{baseUrl}}/verifications/{{verification_id}}/reject
   Body: {
     "reasonForRejection": "Missing documents",
     "adminId": "{{admin_id}}"
   }
   Expected: 200 OK or 404 Not Found
```

#### **👥 User Management Requests (GET, PATCH)**
```
7. Get Users List
   Method: GET
   URL: {{baseUrl}}/users?page=1&limit=10
   Expected: 200 OK with user list

8. Get Contractors
   Method: GET
   URL: {{baseUrl}}/contractors?page=1&limit=10
   Expected: 200 OK with contractor list

9. Update User Status (PATCH)
   Method: PATCH
   URL: {{baseUrl}}/users/{{user_id}}/status
   Body: {
     "status": "suspended",
     "reason": "Policy violation",
     "adminId": "{{admin_id}}"
   }
   Expected: 200 OK or 404 Not Found
```

#### **🏗️ Site Management Requests (GET, PATCH)**
```
10. Get Sites List
    Method: GET
    URL: {{baseUrl}}/sites?page=1&limit=10
    Expected: 200 OK with site list

11. Get Sites by Location
    Method: GET
    URL: {{baseUrl}}/sites/location?state=Karnataka
    Expected: 200 OK with filtered sites

12. Update Site Status (PATCH)
    Method: PATCH
    URL: {{baseUrl}}/sites/{{site_id}}/status
    Body: {
      "status": "approved",
      "adminNotes": "Site meets requirements",
      "adminId": "{{admin_id}}"
    }
    Expected: 200 OK or 404 Not Found
```

#### **💰 Service Management Requests (GET, POST, DELETE)**
```
13. Get Transactions
    Method: GET
    URL: {{baseUrl}}/transactions?page=1&limit=10
    Expected: 200 OK with transaction list

14. Broadcast Notification (POST)
    Method: POST
    URL: {{baseUrl}}/notifications/broadcast
    Body: {
      "title": "System Maintenance",
      "message": "Scheduled maintenance notice",
      "type": "info",
      "adminId": "{{admin_id}}"
    }
    Expected: 200 OK

15. Delete Rating (DELETE)
    Method: DELETE
    URL: {{baseUrl}}/ratings/{{rating_id}}
    Expected: 200 OK or 404 Not Found
```

## 🎯 Testing Workflow in Postman Extension

### **Individual Request Testing:**
1. **Select Request** from collection tree
2. **Review Headers** - ensure Authorization and Session are set
3. **Check Body** (for POST/PATCH requests)
4. **Click "Send"**
5. **Analyze Response:**
   - Status code (200, 400, 404, etc.)
   - Response body structure
   - Response time
   - Test results (pass/fail)

### **Collection Runner Testing:**
1. **Right-click** on collection name
2. **Select "Run Collection"**
3. **Configure:**
   - Environment: "Admin Management Service Environment"
   - Iterations: 1
   - Delay: 100ms between requests
4. **Click "Run"**
5. **Monitor Results** in real-time

## 📊 Expected Results Summary

### **✅ Should Pass (27+ requests):**
- All GET requests for data retrieval
- POST requests with valid payloads
- PATCH requests with proper body structure
- Authentication working across all endpoints

### **⚠️ May Fail (Expected):**
- PATCH/DELETE requests with non-existent IDs (404)
- Requests with invalid payloads (400)
- Unauthorized requests without tokens (401)

### **🎯 Target Metrics:**
- **Success Rate:** 79.4%+ (27/34 requests)
- **Response Time:** < 100ms average
- **Authentication:** 100% working
- **Error Handling:** Consistent and informative

## 🔧 Troubleshooting Guide

### **Common Issues & Solutions:**

#### **❌ "Cannot connect to localhost:3001"**
**Solution:**
```bash
# Check if admin service is running
netstat -an | findstr :3001

# If not running, start it
npm start
```

#### **❌ "401 Unauthorized"**
**Solution:**
```bash
# Refresh tokens
node refresh-postman-tokens.js

# Re-import updated collection in Postman extension
```

#### **❌ "400 Bad Request" on PATCH/POST**
**Solution:**
- Check request body format in Postman
- Ensure all required fields are included
- Verify JSON syntax is valid

#### **❌ Tests Failing**
**Solution:**
- Check "Tests" tab in Postman response
- Review assertion errors
- Verify response structure matches expectations

## 🎉 Success Indicators

### **✅ Everything Working When You See:**
- Dashboard requests return verification statistics
- User lists show paginated results
- Site management returns location-based data
- Notifications can be broadcast successfully
- Error responses are properly formatted JSON
- Response times are consistently under 100ms

### **🎯 Key Validation Points:**
1. **Authentication:** All requests include proper headers
2. **Pagination:** List endpoints support page/limit parameters
3. **Filtering:** Endpoints support status, role, location filters
4. **Validation:** Invalid requests return appropriate error codes
5. **Performance:** Response times are acceptable

## 🏆 Final Checklist

- [ ] Postman extension installed and configured
- [ ] Collection and environment imported
- [ ] Fresh authentication tokens loaded
- [ ] Admin service running on port 3001
- [ ] User service running on port 3007
- [ ] All GET requests tested and working
- [ ] All POST/PATCH/DELETE requests tested
- [ ] Error handling validated
- [ ] Performance metrics recorded

**🎯 Ready for comprehensive testing with 79.4%+ expected success rate!**

---

**📁 Files Created:**
- `postman-collection-complete.json` - Complete API collection
- `postman-environment.json` - Environment with fresh tokens
- `refresh-postman-tokens.js` - Token refresh utility
- `POSTMAN_VSCODE_INSTRUCTIONS.md` - Detailed instructions
- `middleware/auth-fix.js` - Authentication fix (already applied)
