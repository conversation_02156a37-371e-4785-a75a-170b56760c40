# Admin Management Service - Postman Extension Testing Guide

## 🚀 Quick Setup Instructions

### Step 1: Install Postman Extension
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Postman"
4. Install the official Postman extension
5. Sign in to your Postman account (or create one)

### Step 2: Import Collection and Environment
1. **Open Postman Extension** in VS Code
2. **Import Collection:**
   - Click "Import" in Postman extension
   - Select `postman-collection-enhanced.json`
   - Collection will appear as "Enhanced Admin Management Service API"

3. **Import Environment:**
   - Click "Import" in Postman extension
   - Select `postman-environment.json`
   - Environment will appear as "Admin Management Service Environment"

4. **Select Environment:**
   - In Postman extension, select "Admin Management Service Environment" from dropdown

## 📋 Complete Request Testing Checklist

### ✅ **DASHBOARD & ANALYTICS (2 requests)**

#### 1. GET Admin Dashboard
- **Method:** GET
- **URL:** `{{baseUrl}}/`
- **Headers:** 
  - `Authorization: Bearer {{jwt_token}}`
  - `Session: {{session_id}}`
- **Expected:** 200 OK with dashboard statistics

#### 2. GET Dashboard Analytics
- **Method:** GET
- **URL:** `{{baseUrl}}/analytics`
- **Headers:** 
  - `Authorization: Bearer {{jwt_token}}`
  - `Session: {{session_id}}`
- **Expected:** 200 OK with analytics data

### ✅ **VERIFICATION MANAGEMENT (10 requests)**

#### 3. GET Verification Requests List
- **Method:** GET
- **URL:** `{{baseUrl}}/verifications?page=1&limit=10`
- **Expected:** 200 OK with paginated verification requests

#### 4. GET Verification Statistics
- **Method:** GET
- **URL:** `{{baseUrl}}/verifications/stats?timeframe=30d`
- **Expected:** 200 OK with verification statistics

#### 5. GET Single Verification Request
- **Method:** GET
- **URL:** `{{baseUrl}}/verifications/{{verification_id}}`
- **Expected:** 200 OK or 404 Not Found

#### 6. PATCH Approve Verification
- **Method:** PATCH
- **URL:** `{{baseUrl}}/verifications/{{verification_id}}/approve`
- **Body (JSON):**
```json
{
  "reasonForApproval": "All documents verified successfully",
  "adminId": "689421a8575779a37874d557"
}
```

#### 7. PATCH Reject Verification
- **Method:** PATCH
- **URL:** `{{baseUrl}}/verifications/{{verification_id}}/reject`
- **Body (JSON):**
```json
{
  "reasonForRejection": "Missing required documents",
  "adminId": "689421a8575779a37874d557"
}
```

#### 8. PATCH Update Verification Priority
- **Method:** PATCH
- **URL:** `{{baseUrl}}/verifications/{{verification_id}}/priority`
- **Body (JSON):**
```json
{
  "priority": "high"
}
```

#### 9. PATCH Add Verification Notes
- **Method:** PATCH
- **URL:** `{{baseUrl}}/verifications/{{verification_id}}/notes`
- **Body (JSON):**
```json
{
  "notes": "Additional verification required for business license"
}
```

### ✅ **USER MANAGEMENT (8 requests)**

#### 10. GET Users List
- **Method:** GET
- **URL:** `{{baseUrl}}/users?page=1&limit=10&role=contractor`

#### 11. GET User Statistics
- **Method:** GET
- **URL:** `{{baseUrl}}/users/stats?timeframe=30d`

#### 12. GET Single User
- **Method:** GET
- **URL:** `{{baseUrl}}/users/{{user_id}}`

#### 13. GET Contractors List
- **Method:** GET
- **URL:** `{{baseUrl}}/contractors?page=1&limit=10`

#### 14. GET Brokers List
- **Method:** GET
- **URL:** `{{baseUrl}}/brokers?page=1&limit=10`

#### 15. PATCH Update User Status
- **Method:** PATCH
- **URL:** `{{baseUrl}}/users/{{user_id}}/status`
- **Body (JSON):**
```json
{
  "status": "suspended",
  "reason": "Policy violation - spam reports",
  "adminId": "689421a8575779a37874d557"
}
```

### ✅ **SITE MANAGEMENT (9 requests)**

#### 16. GET Sites List
- **Method:** GET
- **URL:** `{{baseUrl}}/sites?page=1&limit=10&status=pending`

#### 17. GET Site Statistics
- **Method:** GET
- **URL:** `{{baseUrl}}/sites/stats?timeframe=30d`

#### 18. GET Sites by Location
- **Method:** GET
- **URL:** `{{baseUrl}}/sites/location?state=Karnataka`

#### 19. GET Single Site
- **Method:** GET
- **URL:** `{{baseUrl}}/sites/{{site_id}}`

#### 20. GET Project Requirements
- **Method:** GET
- **URL:** `{{baseUrl}}/projects?page=1&limit=10`

#### 21. GET Service Requests
- **Method:** GET
- **URL:** `{{baseUrl}}/service-requests?page=1&limit=10`

#### 22. PATCH Update Site Status
- **Method:** PATCH
- **URL:** `{{baseUrl}}/sites/{{site_id}}/status`
- **Body (JSON):**
```json
{
  "status": "approved",
  "adminNotes": "Site meets all safety and legal requirements",
  "adminId": "689421a8575779a37874d557"
}
```

### ✅ **SERVICE MANAGEMENT (10 requests)**

#### 23. GET Transactions List
- **Method:** GET
- **URL:** `{{baseUrl}}/transactions?page=1&limit=10`

#### 24. GET Single Transaction
- **Method:** GET
- **URL:** `{{baseUrl}}/transactions/{{transaction_id}}`

#### 25. GET Ratings List
- **Method:** GET
- **URL:** `{{baseUrl}}/ratings?page=1&limit=10`

#### 26. DELETE Rating
- **Method:** DELETE
- **URL:** `{{baseUrl}}/ratings/{{rating_id}}`

#### 27. GET Notifications List
- **Method:** GET
- **URL:** `{{baseUrl}}/notifications?page=1&limit=10`

#### 28. POST Broadcast Notification
- **Method:** POST
- **URL:** `{{baseUrl}}/notifications/broadcast`
- **Body (JSON):**
```json
{
  "title": "System Maintenance Notice",
  "message": "Scheduled maintenance will occur on Sunday from 2 AM to 4 AM IST",
  "type": "info",
  "adminId": "689421a8575779a37874d557"
}
```

#### 29. GET Support Tickets
- **Method:** GET
- **URL:** `{{baseUrl}}/support/tickets?page=1&limit=10&status=open`

#### 30. PATCH Update Support Ticket Status
- **Method:** PATCH
- **URL:** `{{baseUrl}}/support/tickets/{{ticket_id}}/status`
- **Body (JSON):**
```json
{
  "status": "resolved",
  "resolution": "Issue resolved by updating user permissions",
  "adminId": "689421a8575779a37874d557"
}
```

## 🔧 Testing Instructions

### **Method 1: Individual Request Testing**
1. **Select a request** from the collection
2. **Check headers** - ensure Authorization and Session headers are set
3. **Review body** (for POST/PATCH requests)
4. **Click Send**
5. **Verify response** status and data

### **Method 2: Collection Runner**
1. **Right-click** on collection name
2. **Select "Run Collection"**
3. **Choose environment**
4. **Click "Run"**
5. **Review results** in runner tab

### **Method 3: Automated Testing**
1. Each request has **test scripts** that automatically validate:
   - Response status codes
   - Response structure
   - Data types
   - Required fields

## 📊 Expected Results

### **Success Scenarios:**
- **GET requests:** 200 OK with data
- **POST requests:** 200/201 Created
- **PATCH requests:** 200 OK with updated data
- **DELETE requests:** 200 OK or 204 No Content

### **Error Scenarios:**
- **401 Unauthorized:** Invalid/expired tokens
- **400 Bad Request:** Invalid request data
- **404 Not Found:** Resource doesn't exist
- **500 Internal Server Error:** Server issues

## 🚨 Troubleshooting

### **Common Issues:**

#### **401 Unauthorized Errors:**
- **Solution:** Refresh tokens by running `node debug-auth.js`
- Update `jwt_token` and `session_id` in environment

#### **Connection Refused:**
- **Solution:** Ensure admin service is running on port 3001
- Check if all services are started: `npm start` in root directory

#### **404 Not Found:**
- **Solution:** Verify the endpoint URL is correct
- Check if the resource ID exists in database

#### **400 Bad Request:**
- **Solution:** Check request body format
- Ensure all required fields are included

## 🎯 Testing Checklist

- [ ] Import collection and environment
- [ ] Verify fresh authentication tokens
- [ ] Test all GET requests (read operations)
- [ ] Test all POST requests (create operations)
- [ ] Test all PATCH requests (update operations)
- [ ] Test all DELETE requests (delete operations)
- [ ] Verify error handling (invalid IDs, unauthorized access)
- [ ] Check response times and performance
- [ ] Validate response data structure
- [ ] Test pagination and filtering

## 🏆 Success Metrics

**Target Results:**
- **27+ requests** should return 200 OK
- **Authentication** should work for all requests
- **Response times** should be under 100ms
- **Error handling** should be consistent

**Current Status:** ✅ **79.4% Success Rate** (27/34 requests working)
