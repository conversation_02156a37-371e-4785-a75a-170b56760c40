# 🚀 VS Code Postman Extension - Step-by-Step Testing Guide

## 📋 Prerequisites Checklist

### ✅ **Before Starting:**
- [ ] Admin Management Service is running on port 3001
- [ ] User Management Service is running on port 3007
- [ ] MongoDB is running and accessible
- [ ] Redis is running and accessible
- [ ] VS Code is installed with Postman extension

## 🔧 Step 1: Install Postman Extension

1. **Open VS Code**
2. **Go to Extensions** (Ctrl+Shift+X or Cmd+Shift+X)
3. **Search for "Postman"**
4. **Install** the official "Postman" extension by Postman
5. **Restart VS Code** if prompted

## 📥 Step 2: Import Collection

1. **Open Postman Extension**
   - Click on the Postman icon in the VS Code sidebar
   - Or use Command Palette (Ctrl+Shift+P) → "Postman: Focus on Postman View"

2. **Sign In to Postman**
   - Click "Sign In" in the Postman panel
   - Use your Postman account or create a new one

3. **Import Collection**
   - Click "Import" button in Postman panel
   - Select "File" option
   - Navigate to: `admin-management-service/postman-collection-complete.json`
   - Click "Import"

4. **Import Environment**
   - Click "Import" button again
   - Select "File" option
   - Navigate to: `admin-management-service/postman-environment.json`
   - Click "Import"

## ⚙️ Step 3: Configure Environment

1. **Select Environment**
   - In Postman panel, find the environment dropdown
   - Select "Admin Management Service Environment"

2. **Verify Variables**
   - Click on the environment name to view variables
   - Ensure these variables are set:
     - `baseUrl`: http://localhost:3001/admin-service/api/v1
     - `jwt_token`: (should be auto-populated)
     - `session_id`: (should be auto-populated)

## 🧪 Step 4: Test Individual Requests

### **Method A: Single Request Testing**

1. **Expand Collection**
   - Click on "Complete Admin Management Service API"
   - Expand folders (🏠 Dashboard & Analytics, 🔍 Verification Management, etc.)

2. **Select a Request**
   - Click on any request (e.g., "Get Admin Dashboard")
   - Request details will appear in the main panel

3. **Review Request**
   - Check the URL: `{{baseUrl}}/`
   - Verify headers include:
     - `Authorization: Bearer {{jwt_token}}`
     - `Session: {{session_id}}`

4. **Send Request**
   - Click the "Send" button
   - View response in the bottom panel

5. **Check Results**
   - Status code should be 200 OK
   - Response body should contain JSON data
   - Tests tab shows passed/failed assertions

### **Method B: Collection Runner**

1. **Run Entire Collection**
   - Right-click on collection name
   - Select "Run Collection"

2. **Configure Run**
   - Select environment: "Admin Management Service Environment"
   - Choose requests to run (or select all)
   - Set iterations: 1
   - Set delay: 0ms

3. **Start Run**
   - Click "Run Complete Admin Management Service API"
   - Monitor progress in runner window

4. **Review Results**
   - Check pass/fail status for each request
   - View detailed results and response times
   - Export results if needed

## 📊 Step 5: Test All Request Types

### **🏠 Dashboard & Analytics (2 requests)**
1. **Get Admin Dashboard** - GET `/`
2. **Get Dashboard Analytics** - GET `/analytics`

### **🔍 Verification Management (7 requests)**
1. **Get Verification Requests** - GET `/verifications`
2. **Get Verification Statistics** - GET `/verifications/stats`
3. **Get Single Verification** - GET `/verifications/{id}`
4. **Approve Verification** - PATCH `/verifications/{id}/approve`
5. **Reject Verification** - PATCH `/verifications/{id}/reject`
6. **Update Priority** - PATCH `/verifications/{id}/priority`
7. **Add Notes** - PATCH `/verifications/{id}/notes`

### **👥 User Management (3 requests)**
1. **Get Users List** - GET `/users`
2. **Get Contractors** - GET `/contractors`
3. **Update User Status** - PATCH `/users/{id}/status`

### **💰 Service Management (1 request)**
1. **Broadcast Notification** - POST `/notifications/broadcast`

## 🔍 Step 6: Analyze Results

### **Expected Success Rates:**
- **Dashboard & Analytics:** 100% (2/2)
- **Verification Management:** ~85% (6/7)
- **User Management:** ~85% (2-3/3)
- **Service Management:** ~90% (1/1)

### **Common Response Codes:**
- **200 OK:** Request successful
- **400 Bad Request:** Invalid request data (check payload)
- **401 Unauthorized:** Authentication issue (refresh tokens)
- **404 Not Found:** Resource doesn't exist
- **500 Internal Server Error:** Server issue

## 🛠️ Step 7: Troubleshooting

### **❌ 401 Unauthorized Errors**
**Solution:**
1. Run authentication script: `node debug-auth.js`
2. Copy new tokens from output
3. Update collection variables:
   - Right-click collection → "Edit"
   - Update `jwt_token` and `session_id` values

### **❌ Connection Refused**
**Solution:**
1. Check if admin service is running: `netstat -an | findstr :3001`
2. If not running, start services: `npm start` in root directory
3. Wait for "Admin service listening on port 3001" message

### **❌ 400 Bad Request on PATCH/POST**
**Solution:**
1. Check request body format in Postman
2. Ensure all required fields are included
3. Verify JSON syntax is correct

### **❌ Tests Failing**
**Solution:**
1. Check the "Tests" tab in request results
2. Review assertion errors
3. Verify response structure matches expectations

## 📈 Step 8: Monitor Performance

### **Performance Metrics to Check:**
- **Response Time:** Should be < 100ms for most requests
- **Success Rate:** Target 80%+ overall
- **Error Patterns:** Identify common failure points

### **Using Test Results:**
1. **Export Results:** Click "Export Results" in runner
2. **Generate Reports:** Use exported JSON for analysis
3. **Track Improvements:** Compare results over time

## 🎯 Step 9: Advanced Testing

### **Custom Test Scripts:**
Each request includes test scripts that verify:
- Response status codes
- Response structure
- Data types and required fields
- Performance benchmarks

### **Environment Variables:**
Use variables for dynamic testing:
- `{{verification_id}}` - Auto-populated from responses
- `{{user_id}}` - Set from user list responses
- `{{admin_id}}` - Admin user identifier

### **Pre-request Scripts:**
Collection includes scripts that:
- Validate token presence
- Set dynamic variables
- Log warnings for missing data

## ✅ Success Checklist

- [ ] All GET requests return 200 OK
- [ ] Authentication works across all endpoints
- [ ] POST/PATCH requests handle validation correctly
- [ ] Error responses are properly formatted
- [ ] Response times are acceptable (< 100ms)
- [ ] Test assertions pass consistently

## 🎉 Expected Results

**Target Outcome:**
- **27+ requests** should pass successfully
- **Authentication** working for all endpoints
- **Error handling** consistent and informative
- **Performance** excellent (sub-100ms responses)

**Current Status:** ✅ **79.4% Success Rate** achieved in previous testing

---

**🔗 Quick Links:**
- Collection File: `postman-collection-complete.json`
- Environment File: `postman-environment.json`
- Authentication Script: `debug-auth.js`
- Full Test Report: `FINAL_TEST_REPORT.md`
