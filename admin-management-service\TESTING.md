# Admin Management Service - API Testing Guide

## 🚀 Quick Start

### Prerequisites
1. **Service Running**: Start the admin-management-service
   ```bash
   cd admin-management-service
   npm start
   ```

2. **Dependencies**: MongoDB and Redis must be running
   ```bash
   # MongoDB (default port 27017)
   mongod
   
   # Redis (default port 6379)
   redis-server
   ```

3. **JWT Token**: You need a valid JWT token for authentication
   - Get token from your authentication service
   - Update `postman-environment.json` with the token

## 📋 Testing Methods

### Method 1: Using Postman Extension in VS Code
1. Install the Postman extension in VS Code
2. Open the collection file: `postman-collection.json`
3. Import the environment: `postman-environment.json`
4. Update the `jwt_token` in environment variables
5. Run individual tests or the entire collection

### Method 2: Using Newman (Command Line)
```bash
# Install newman globally
npm install -g newman

# Run all tests
newman run postman-collection.json -e postman-environment.json

# Run with detailed output
newman run postman-collection.json -e postman-environment.json --reporters cli,json --reporter-json-export test-results.json
```

### Method 3: Using the Test Runner Script
```bash
# Make the script executable
chmod +x run-postman-tests.js

# Run tests
node run-postman-tests.js

# Get help
node run-postman-tests.js --help
```

## 🧪 Test Cases Included

### 1. **Get Admin Dashboard Data**
- **Endpoint**: `GET /`
- **Tests**: Response structure, status code, response time
- **Expected**: Dashboard data with verification request counts

### 2. **Get Verification Requests - Default**
- **Endpoint**: `GET /verifications`
- **Tests**: Pagination structure, array response, status code
- **Expected**: List of verification requests with pagination

### 3. **Get Verification Requests - With Filters**
- **Endpoint**: `GET /verifications?status=pending&type=contractor&limit=5`
- **Tests**: Filter application, pagination limits
- **Expected**: Filtered results matching criteria

### 4. **Get Single Verification Request**
- **Endpoint**: `GET /verifications/{id}`
- **Tests**: Single record retrieval, 404 handling
- **Expected**: Single verification request or 404 error

### 5. **Test Unauthorized Access**
- **Endpoint**: `GET /verifications` (without auth)
- **Tests**: 401 status code, error message
- **Expected**: Unauthorized error response

### 6. **Test Invalid Verification ID**
- **Endpoint**: `GET /verifications/invalid_id_123`
- **Tests**: Error handling for invalid IDs
- **Expected**: 404 or 500 error with message

### 7. **Test Invalid Query Parameters**
- **Endpoint**: `GET /verifications?status=invalid&limit=-1`
- **Tests**: Graceful handling of invalid parameters
- **Expected**: Default values applied, no crashes

## 🔧 Environment Variables

Update `postman-environment.json` with your values:

```json
{
  "baseUrl": "http://localhost:3001/admin-service/api/v1",
  "jwt_token": "your_actual_jwt_token_here",
  "host": "localhost",
  "port": "3001"
}
```

## 📊 Expected Test Results

### Successful Test Run Should Show:
- ✅ All status code tests passing
- ✅ Response structure validation passing
- ✅ Authentication tests working correctly
- ✅ Error handling tests confirming proper error responses

### Common Issues and Solutions:

1. **401 Unauthorized**
   - Solution: Update JWT token in environment variables
   - Check if token is expired

2. **Connection Refused**
   - Solution: Ensure service is running on correct port
   - Check if MongoDB/Redis are running

3. **404 Not Found**
   - Solution: Verify base URL and endpoint paths
   - Check service routing configuration

4. **Empty Response Arrays**
   - Solution: Add test data to MongoDB
   - Check database connection

## 🗄️ Test Data Setup

To populate test data for verification requests:

```javascript
// MongoDB shell commands
use admin_management_service;

// Insert sample verification requests
db.verificationrequests.insertMany([
  {
    type: "contractor",
    requesterId: "user123",
    status: "pending",
    varifiedBy: null,
    reasonForRejection: ""
  },
  {
    type: "broker",
    requesterId: "user456",
    status: "approved",
    varifiedBy: ObjectId("674a1b2c3d4e5f6789012501"),
    reasonForRejection: ""
  }
]);
```

## 🔍 Debugging Tips

1. **Check Service Logs**: Monitor console output for errors
2. **Verify Database**: Ensure collections exist and have data
3. **Test Authentication**: Verify JWT token is valid and not expired
4. **Network Issues**: Check if ports are accessible
5. **Environment Variables**: Confirm all required env vars are set

## 📈 Continuous Testing

For automated testing in CI/CD:

```bash
# Add to package.json scripts
"scripts": {
  "test:api": "newman run postman-collection.json -e postman-environment.json --reporters cli,junit --reporter-junit-export test-results.xml"
}

# Run in CI
npm run test:api
```
