const express = require('express');
const mongoose = require('mongoose');
const { Topics } = require('@build-connect/utils/constants/topics');
const log = require('@build-connect/utils/log');
const cookieParser = require('cookie-parser');
const { Ka<PERSON>ka, handlersMap } = require('./pubsub');

const { connectRedis } = require('./cache');
const { DATABASE_URL, PORT } = require('./config').getAll();

// routes
const routes = require('./routes/routes');

// topics for subscription
const topicsForSubscription = [Topics.VERFICATION_REQUESTED];

const app = express();

app.use(cookieParser());
app.use(express.json());

app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, PATCH, DELETE, OPTIONS'
    );
    res.setHeader(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization'
    );
    next();
});

app.use('/admin-service/api/v1', routes);

app.use((err, _req, res, _next) => {
    const { statusCode = 500, data } = err;
    log(err);
    if (data && data.length > 0) {
        res.status(statusCode).json({ message: data });
        return;
    }
    let { message } = err;
    if (!err.message) message = 'something went wrong, try again!';
    res.status(statusCode).json({ message });
});

async function main() {
    await mongoose.connect(DATABASE_URL);
    await connectRedis();

    // Connect Kafka producer for publishing events
    await Kafka.connectProducer();

    // Subscribe to topics for consuming events
    await Kafka.subscribe(topicsForSubscription, handlersMap);

    app.listen(PORT, () =>
        // eslint-disable-next-line no-console
        console.log(`Admin service listening on port ${PORT}`)
    );
}

main().catch((err) => console.error(err));

// Handle graceful shutdown
process.on('SIGINT', async () => {
    await Kafka.disconnectProducer();
    await Kafka.disconnectConsumer();
    process.exit(0);
});
