const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3001/admin-service/api/v1';
const USER_SERVICE_URL = 'http://localhost:3007/user-service/api/v1';

// Test credentials
const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'Build@123'
};

// Global auth tokens
let authTokens = {
    accessToken: null,
    sessionId: null
};

// Test results tracking
const testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
};

// Utility functions
function logTest(name, status, response, error = null) {
    testResults.total++;
    const result = {
        name,
        status,
        response: typeof response === 'object' ? JSON.stringify(response).substring(0, 200) : response,
        error: error ? error.message : null,
        timestamp: new Date().toISOString()
    };
    
    if (status === 'PASS') {
        testResults.passed++;
        console.log(`✅ ${name}`);
    } else {
        testResults.failed++;
        console.log(`❌ ${name}: ${error ? error.message : 'Unknown error'}`);
    }
    
    testResults.details.push(result);
}

// Authentication function
async function authenticate() {
    try {
        console.log('🔐 Authenticating admin user...');
        
        const response = await fetch(`${USER_SERVICE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ADMIN_CREDENTIALS)
        });

        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.accessToken || !data.sessionId) {
            throw new Error('Authentication response missing required tokens');
        }

        authTokens.accessToken = data.accessToken;
        authTokens.sessionId = data.sessionId;
        
        console.log('✅ Authentication successful!');
        console.log(`   Session ID: ${authTokens.sessionId.substring(0, 20)}...`);
        console.log(`   Access Token: ${authTokens.accessToken.substring(0, 20)}...`);
        
        return true;
    } catch (error) {
        console.error('❌ Authentication failed:', error.message);
        return false;
    }
}

// Generic API test function
async function testEndpoint(method, endpoint, body = null, expectedStatus = 200) {
    try {
        const url = `${BASE_URL}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authTokens.accessToken}`,
                'Session': authTokens.sessionId
            }
        };

        if (body && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(url, options);
        const responseData = await response.text();
        
        let parsedData;
        try {
            parsedData = JSON.parse(responseData);
        } catch {
            parsedData = responseData;
        }

        const testName = `${method} ${endpoint}`;
        
        if (response.status === expectedStatus || (Array.isArray(expectedStatus) && expectedStatus.includes(response.status))) {
            logTest(testName, 'PASS', parsedData);
            return { success: true, data: parsedData, status: response.status };
        } else {
            logTest(testName, 'FAIL', parsedData, new Error(`Expected ${expectedStatus}, got ${response.status}`));
            return { success: false, data: parsedData, status: response.status };
        }
    } catch (error) {
        const testName = `${method} ${endpoint}`;
        logTest(testName, 'FAIL', null, error);
        return { success: false, error: error.message };
    }
}

// Test suites
async function testDashboardAndAnalytics() {
    console.log('\n📊 Testing Dashboard & Analytics...');
    
    await testEndpoint('GET', '/');
    await testEndpoint('GET', '/analytics');
}

async function testVerificationManagement() {
    console.log('\n🔍 Testing Verification Management...');
    
    // Basic verification endpoints
    await testEndpoint('GET', '/verifications?page=1&limit=5');
    await testEndpoint('GET', '/verifications/stats?timeframe=30d');
    
    // Test with a sample verification ID (this might not exist, so we expect 404)
    await testEndpoint('GET', '/verifications/689421a8575779a37874d557', null, [200, 404]);
    
    // Test approval/rejection (these will likely fail due to missing verification IDs)
    await testEndpoint('PATCH', '/verifications/689421a8575779a37874d557/approve', 
        { reasonForApproval: 'Test approval' }, [200, 404]);
    await testEndpoint('PATCH', '/verifications/689421a8575779a37874d557/reject', 
        { reasonForRejection: 'Test rejection' }, [200, 404]);
    
    // Test priority and notes updates
    await testEndpoint('PATCH', '/verifications/689421a8575779a37874d557/priority', 
        { priority: 'high' }, [200, 404]);
    await testEndpoint('PATCH', '/verifications/689421a8575779a37874d557/notes', 
        { notes: 'Test notes' }, [200, 404]);
}

async function testUserManagement() {
    console.log('\n👥 Testing User Management...');
    
    await testEndpoint('GET', '/users?page=1&limit=5');
    await testEndpoint('GET', '/users/stats?timeframe=30d');
    await testEndpoint('GET', '/contractors?page=1&limit=5');
    await testEndpoint('GET', '/brokers?page=1&limit=5');
    
    // Test with sample user ID
    await testEndpoint('GET', '/users/689421a8575779a37874d557', null, [200, 404]);
    await testEndpoint('PATCH', '/users/689421a8575779a37874d557/status',
        { isActive: true, reason: 'Test activation' }, [200, 404]);
}

async function testSiteManagement() {
    console.log('\n🏗️ Testing Site Management...');
    
    await testEndpoint('GET', '/sites?page=1&limit=5');
    await testEndpoint('GET', '/sites/stats?timeframe=30d');
    await testEndpoint('GET', '/sites/location?state=Karnataka');
    await testEndpoint('GET', '/projects?page=1&limit=5');
    await testEndpoint('GET', '/service-requests?page=1&limit=5');
    
    // Test with sample site ID
    await testEndpoint('GET', '/sites/689421a8575779a37874d557', null, [200, 404]);
    await testEndpoint('PATCH', '/sites/689421a8575779a37874d557/status', 
        { status: 'active' }, [200, 404]);
}

async function testServiceManagement() {
    console.log('\n💰 Testing Service Management...');
    
    await testEndpoint('GET', '/transactions?page=1&limit=5');
    await testEndpoint('GET', '/ratings?page=1&limit=5');
    await testEndpoint('GET', '/notifications?page=1&limit=5');
    await testEndpoint('GET', '/support/tickets?page=1&limit=5');
    
    // Test with sample IDs
    await testEndpoint('GET', '/transactions/689421a8575779a37874d557', null, [200, 404]);
    await testEndpoint('DELETE', '/ratings/689421a8575779a37874d557', null, [200, 404]);
    
    // Test broadcast notification
    await testEndpoint('POST', '/notifications/broadcast', {
        title: 'Test Notification',
        message: 'This is a test broadcast notification',
        type: 'info'
    }, [200, 400, 500]);
    
    // Test support ticket status update
    await testEndpoint('PATCH', '/support/tickets/689421a8575779a37874d557/status', 
        { status: 'resolved' }, [200, 404]);
}

async function testErrorHandling() {
    console.log('\n🚨 Testing Error Handling & Edge Cases...');
    
    // Test invalid endpoints
    await testEndpoint('GET', '/invalid-endpoint', null, 404);
    
    // Test invalid IDs
    await testEndpoint('GET', '/verifications/invalid-id', null, [400, 500]);
    
    // Test without authentication (temporarily remove tokens)
    const tempTokens = { ...authTokens };
    authTokens.accessToken = null;
    authTokens.sessionId = null;
    
    await testEndpoint('GET', '/verifications', null, 401);
    
    // Restore tokens
    authTokens = tempTokens;
    
    // Test with invalid query parameters
    await testEndpoint('GET', '/verifications?page=-1&limit=abc&status=invalid');
}

// Main test execution function
async function runAllTests() {
    console.log('🚀 COMPREHENSIVE ADMIN MANAGEMENT SERVICE ROUTE TESTING');
    console.log('================================================================================');

    // Step 1: Authenticate
    const authSuccess = await authenticate();
    if (!authSuccess) {
        console.error('❌ Cannot proceed without authentication. Please check user service and credentials.');
        return;
    }

    // Step 2: Run all test suites
    try {
        await testDashboardAndAnalytics();
        await testVerificationManagement();
        await testUserManagement();
        await testSiteManagement();
        await testServiceManagement();
        await testErrorHandling();
    } catch (error) {
        console.error('❌ Test execution error:', error.message);
    }

    // Step 3: Generate summary report
    generateSummaryReport();
}

// Generate and display summary report
function generateSummaryReport() {
    console.log('\n================================================================================');
    console.log('📋 COMPREHENSIVE TEST SUMMARY REPORT');
    console.log('================================================================================');

    const successRate = testResults.total > 0 ? ((testResults.passed / testResults.total) * 100).toFixed(1) : 0;

    console.log(`\n🎯 OVERALL RESULTS:`);
    console.log(`   Total Tests: ${testResults.total}`);
    console.log(`   ✅ Passed: ${testResults.passed}`);
    console.log(`   ❌ Failed: ${testResults.failed}`);
    console.log(`   📊 Success Rate: ${successRate}%`);

    // Categorize results
    const categories = {
        'Dashboard & Analytics': [],
        'Verification Management': [],
        'User Management': [],
        'Site Management': [],
        'Service Management': [],
        'Error Handling': []
    };

    testResults.details.forEach(test => {
        if (test.name.includes('/analytics') || test.name === 'GET /') {
            categories['Dashboard & Analytics'].push(test);
        } else if (test.name.includes('/verifications')) {
            categories['Verification Management'].push(test);
        } else if (test.name.includes('/users') || test.name.includes('/contractors') || test.name.includes('/brokers')) {
            categories['User Management'].push(test);
        } else if (test.name.includes('/sites') || test.name.includes('/projects') || test.name.includes('/service-requests')) {
            categories['Site Management'].push(test);
        } else if (test.name.includes('/transactions') || test.name.includes('/ratings') || test.name.includes('/notifications') || test.name.includes('/support')) {
            categories['Service Management'].push(test);
        } else {
            categories['Error Handling'].push(test);
        }
    });

    console.log(`\n📊 CATEGORY BREAKDOWN:`);
    Object.entries(categories).forEach(([category, tests]) => {
        if (tests.length > 0) {
            const passed = tests.filter(t => t.status === 'PASS').length;
            const total = tests.length;
            const rate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
            console.log(`   ${category.toUpperCase()}: ${passed}/${total} (${rate}%)`);
        }
    });

    // Show failed tests details
    const failedTests = testResults.details.filter(t => t.status === 'FAIL');
    if (failedTests.length > 0) {
        console.log(`\n❌ FAILED TESTS DETAILS:`);
        failedTests.forEach(test => {
            console.log(`   • ${test.name}: ${test.error || 'Unknown error'}`);
        });
    }

    // Recommendations
    console.log(`\n💡 RECOMMENDATIONS:`);
    if (testResults.failed === 0) {
        console.log('   🎉 All tests passed! The admin service is working perfectly.');
    } else {
        console.log('   🔧 Issues found that need attention:');

        const authErrors = failedTests.filter(t => t.error && t.error.includes('401'));
        if (authErrors.length > 0) {
            console.log('   • Authentication issues detected - check JWT token validity and session management');
        }

        const connectionErrors = failedTests.filter(t => t.error && (t.error.includes('ECONNREFUSED') || t.error.includes('fetch failed')));
        if (connectionErrors.length > 0) {
            console.log('   • Connection issues detected - ensure all dependent services are running');
        }

        const serverErrors = failedTests.filter(t => t.error && t.error.includes('500'));
        if (serverErrors.length > 0) {
            console.log('   • Server errors detected - check application logs and database connectivity');
        }
    }

    console.log('\n🎉 Admin Management Service Route Testing Complete!');
    console.log('================================================================================');
}

// Error handling for unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    runAllTests,
    testEndpoint,
    authenticate,
    testResults
};
