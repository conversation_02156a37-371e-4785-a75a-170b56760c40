const mongoose = require('mongoose');
const ExpressError = require('@build-connect/utils/ExpressError');

/**
 * Get all sites with pagination and filtering
 */
exports.getAllSites = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        status = '',
        state = '',
        district = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/sites?page=${page}&limit=${limit}&search=${search}&status=${status}&state=${state}&district=${district}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch sites from site service', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        // Fallback: Return mock data structure
        res.status(200).json({
            sites: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Site service temporarily unavailable'
        });
    }
};

/**
 * Get site by ID
 */
exports.getSiteById = async (req, res) => {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid site ID', 400);
    }

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/sites/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('Site not found', 404);
            }
            throw new ExpressError('Failed to fetch site from site service', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Site service temporarily unavailable', 503);
    }
};

/**
 * Update site status (approve/reject)
 */
exports.updateSiteStatus = async (req, res) => {
    const { id } = req.params;
    const { status, reason } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid site ID', 400);
    }

    if (!['approved', 'rejected', 'pending'].includes(status)) {
        throw new ExpressError('Invalid status. Must be approved, rejected, or pending', 400);
    }

    if (status === 'rejected' && (!reason || reason.trim().length === 0)) {
        throw new ExpressError('Reason is required when rejecting a site', 400);
    }

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/sites/${id}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                status, 
                reason: reason || '', 
                verifiedBy: req.user.id,
                processedAt: new Date()
            })
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('Site not found', 404);
            }
            throw new ExpressError('Failed to update site status', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Site service temporarily unavailable', 503);
    }
};

/**
 * Get site statistics
 */
exports.getSiteStats = async (req, res) => {
    const { timeframe = '30d' } = req.query;

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/stats?timeframe=${timeframe}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch site statistics', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        // Fallback: Return mock statistics
        res.status(200).json({
            timeframe,
            stats: {
                totalSites: 0,
                pendingSites: 0,
                approvedSites: 0,
                rejectedSites: 0,
                newSites: 0,
                averagePrice: 0,
                totalValue: 0,
                sitesByState: {},
                sitesByDistrict: {}
            },
            message: 'Site service temporarily unavailable'
        });
    }
};

/**
 * Get sites by location
 */
exports.getSitesByLocation = async (req, res) => {
    const { state, district, limit = 50 } = req.query;

    if (!state) {
        throw new ExpressError('State parameter is required', 400);
    }

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/sites/location?state=${state}&district=${district || ''}&limit=${limit}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch sites by location', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            sites: [],
            location: { state, district },
            message: 'Site service temporarily unavailable'
        });
    }
};

/**
 * Get project requirements for admin review
 */
exports.getProjectRequirements = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/projects?page=${page}&limit=${limit}&status=${status}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch project requirements', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            projects: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Site service temporarily unavailable'
        });
    }
};

/**
 * Get service requests for admin monitoring
 */
exports.getServiceRequests = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/service-requests?page=${page}&limit=${limit}&status=${status}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch service requests', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            serviceRequests: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Site service temporarily unavailable'
        });
    }
};
