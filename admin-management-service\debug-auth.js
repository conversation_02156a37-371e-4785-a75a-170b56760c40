const fetch = require('node-fetch');
const { client } = require('./cache');

// Configuration
const USER_SERVICE_URL = 'http://localhost:3007/user-service/api/v1';
const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'Build@123'
};

async function debugAuthentication() {
    console.log('🔍 DEBUG: Authentication Flow Analysis');
    console.log('=====================================');
    
    try {
        // Step 1: Connect to Redis
        console.log('1. Connecting to Redis...');
        await client.connect();
        console.log('✅ Redis connected successfully');
        
        // Step 2: Authenticate with user service
        console.log('\n2. Authenticating with user service...');
        const response = await fetch(`${USER_SERVICE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ADMIN_CREDENTIALS)
        });

        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        const authData = await response.json();
        console.log('✅ Authentication successful');
        console.log('   Access Token:', authData.accessToken?.substring(0, 20) + '...');
        console.log('   Session ID:', authData.sessionId?.substring(0, 20) + '...');
        
        // Step 3: Check what's stored in Redis
        console.log('\n3. Checking Redis session data...');
        const sessionData = await client.get(authData.sessionId);
        if (sessionData) {
            const parsedSession = JSON.parse(sessionData);
            console.log('✅ Session found in Redis');
            console.log('   Session contains:', Object.keys(parsedSession));
            console.log('   Access Token in session:', parsedSession.accessToken?.substring(0, 20) + '...');
            console.log('   Refresh Token in session:', parsedSession.refreshToken?.substring(0, 20) + '...');
            console.log('   ID Token in session:', parsedSession.idToken?.substring(0, 20) + '...');
        } else {
            console.log('❌ Session not found in Redis');
        }
        
        // Step 4: Test the authentication middleware logic manually
        console.log('\n4. Testing authentication middleware logic...');
        
        // Simulate what the middleware does
        const sessionId = authData.sessionId;
        const authHeader = `Bearer ${authData.accessToken}`;
        
        console.log('   Session ID from header:', sessionId);
        console.log('   Authorization header:', authHeader.substring(0, 30) + '...');
        
        // Get session from Redis (what middleware does)
        const tokenResponse = await client.get(sessionId);
        if (!tokenResponse) {
            console.log('❌ No token response found in Redis');
            return;
        }
        
        const parsedTokenResponse = JSON.parse(tokenResponse);
        console.log('✅ Token response parsed successfully');
        
        // Check if accessToken exists in the parsed response
        if (parsedTokenResponse.accessToken) {
            console.log('✅ accessToken found in session data');
        } else {
            console.log('❌ accessToken NOT found in session data');
            console.log('   Available keys:', Object.keys(parsedTokenResponse));
        }
        
        // Step 5: Test a simple admin service call
        console.log('\n5. Testing admin service call...');
        try {
            const adminResponse = await fetch('http://localhost:3001/admin-service/api/v1/', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader,
                    'Session': sessionId
                }
            });
            
            const adminData = await adminResponse.text();
            console.log('   Admin service response status:', adminResponse.status);
            console.log('   Admin service response:', adminData.substring(0, 200));
            
        } catch (error) {
            console.log('❌ Admin service call failed:', error.message);
        }
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    } finally {
        if (client.isOpen) {
            await client.disconnect();
        }
    }
}

// Run the debug
debugAuthentication().catch(console.error);
