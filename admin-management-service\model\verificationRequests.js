const mongoose = require('mongoose');

const verificationRequestsSchema = new mongoose.Schema({
    type: {
        type: String,
        enum: ['broker', 'contractor', 'site'],
        required: true,
    },
    requesterId: {
        type: String,
        required: true,
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending',
    },
    verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Admin',
        default: null,
    },
    reasonForRejection: {
        type: String,
        default: '',
    },
    reasonForApproval: {
        type: String,
        default: '',
    },
    processedAt: {
        type: Date,
        default: null,
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium',
    },
    notes: {
        type: String,
        default: '',
    },
    entityData: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
    }
}, {
    timestamps: true,
});

// Add indexes for better query performance
verificationRequestsSchema.index({ status: 1, type: 1 });
verificationRequestsSchema.index({ requesterId: 1 });
verificationRequestsSchema.index({ createdAt: -1 });

const VerificationRequests = mongoose.model(
    'VerificationRequests',
    verificationRequestsSchema
);
module.exports = VerificationRequests;
