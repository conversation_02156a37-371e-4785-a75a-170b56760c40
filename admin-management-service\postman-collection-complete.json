{"info": {"name": "Complete Admin Management Service API", "description": "Comprehensive API testing collection for Admin Management Service - All 34 endpoints with proper payloads", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001/admin-service/api/v1", "type": "string"}, {"key": "jwt_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1NDMyMzUsImV4cCI6MTc1NDU3OTIzNX0.12Uldfibk3BL1RpB1tKtJxG0LHgPwMl03auP0XC9bhQ", "type": "string"}, {"key": "session_id", "value": "tnnijVibRuu7iNYCeVyFVKveEzWDWxqEESTOBc4twL0", "type": "string"}, {"key": "verification_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "user_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "site_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "transaction_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "rating_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "ticket_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "admin_id", "value": "689421a8575779a37874d557", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Collection-level pre-request script", "if (!pm.environment.get('jwt_token') && !pm.collectionVariables.get('jwt_token')) {", "    console.log('Warning: JWT token not set. Please update jwt_token variable.');", "}", "if (!pm.environment.get('session_id') && !pm.collectionVariables.get('session_id')) {", "    console.log('Warning: Session ID not set. Please update session_id variable.');", "}"]}}], "item": [{"name": "🏠 Dashboard & Analytics", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has dashboard data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "});"]}}]}, {"name": "Get Dashboard Analytics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/analytics", "host": ["{{baseUrl}}"], "path": ["analytics"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has analytics data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('object');", "});"]}}]}]}, {"name": "🔍 Verification Management", "item": [{"name": "Get Verification Requests", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications?page=1&limit=10&status=pending", "host": ["{{baseUrl}}"], "path": ["verifications"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "pending"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has verification requests', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('verificationRequests');", "    pm.expect(jsonData.verificationRequests).to.be.an('array');", "    ", "    // Store first verification ID if available", "    if (jsonData.verificationRequests.length > 0) {", "        pm.collectionVariables.set('verification_id', jsonData.verificationRequests[0]._id);", "    }", "});"]}}]}, {"name": "Get Verification Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["verifications", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has statistics', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('stats');", "});"]}}]}, {"name": "Get Single Verification Request", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});"]}}]}, {"name": "Approve Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForApproval\": \"All documents verified successfully\",\n  \"adminId\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/approve", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "approve"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}, {"name": "Reject Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForRejection\": \"Missing required documents\",\n  \"adminId\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/reject", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "reject"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}]}, {"name": "👥 User Management", "item": [{"name": "Get Users List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&limit=10&role=contractor", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "contractor"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has users array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('users');", "    pm.expect(jsonData.users).to.be.an('array');", "});"]}}]}, {"name": "Get Contractors List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/contractors?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["contractors"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has contractors array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('contractors');", "});"]}}]}, {"name": "Update User Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"suspended\",\n  \"reason\": \"Policy violation - spam reports\",\n  \"adminId\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/users/{{user_id}}/status", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}", "status"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200, 400, or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404]);", "});"]}}]}]}, {"name": "💰 Service Management", "item": [{"name": "Broadcast Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"System Maintenance Notice\",\n  \"message\": \"Scheduled maintenance will occur on Sunday from 2 AM to 4 AM IST. Services may be temporarily unavailable.\",\n  \"type\": \"info\",\n  \"adminId\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/notifications/broadcast", "host": ["{{baseUrl}}"], "path": ["notifications", "broadcast"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 400', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400]);", "});"]}}]}]}]}