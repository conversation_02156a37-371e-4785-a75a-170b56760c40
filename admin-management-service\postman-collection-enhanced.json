{"info": {"name": "Enhanced Admin Management Service API", "description": "Complete API testing collection for Enhanced Admin Management Service with verification management, user management, site management, and analytics", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001/admin-service/api/v1", "type": "string"}, {"key": "jwt_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1NDI4NzQsImV4cCI6MTc1NDU3ODg3NH0.Zt7w1W-bpLPgFsl5SQ58Uy8RdPQ8SZozWqtIWaoz24o", "type": "string"}, {"key": "session_id", "value": "VNzLa0asL-NIfaq9HPoygqPZ4TMR86tLXNFRcPAnBhg", "type": "string"}, {"key": "verification_id", "value": "689421a8575779a37874d557", "type": "string"}, {"key": "user_id", "value": "sample_user_id", "type": "string"}, {"key": "site_id", "value": "sample_site_id", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Collection-level pre-request script", "if (!pm.environment.get('jwt_token')) {", "    console.log('Warning: JWT token not set. Please set jwt_token environment variable.');", "}", "if (!pm.environment.get('session_id')) {", "    console.log('Warning: Session ID not set. Please set session_id environment variable.');", "}"]}}], "item": [{"name": "Dashboard & Analytics", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has enhanced dashboard structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "    pm.expect(jsonData).to.have.property('approvedRequests');", "    pm.expect(jsonData).to.have.property('rejectedRequests');", "    pm.expect(jsonData).to.have.property('contractorRequests');", "    pm.expect(jsonData).to.have.property('brokerRequests');", "    pm.expect(jsonData).to.have.property('siteRequests');", "    pm.expect(jsonData).to.have.property('recentActivity');", "    pm.expect(jsonData.recentActivity).to.be.an('array');", "});"]}}]}, {"name": "Get Comprehensive Analytics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/analytics?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["analytics"], "query": [{"key": "timeframe", "value": "30d"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has analytics structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('timeframe');", "    pm.expect(jsonData).to.have.property('overview');", "    pm.expect(jsonData).to.have.property('verifications');", "    pm.expect(jsonData).to.have.property('users');", "    pm.expect(jsonData).to.have.property('sites');", "    pm.expect(jsonData).to.have.property('serviceStatus');", "});"]}}]}]}, {"name": "Verification Management", "item": [{"name": "Get Verification Requests", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications?status=pending&type=contractor&limit=10&page=1", "host": ["{{baseUrl}}"], "path": ["verifications"], "query": [{"key": "status", "value": "pending"}, {"key": "type", "value": "contractor"}, {"key": "limit", "value": "10"}, {"key": "page", "value": "1"}]}}}, {"name": "Approve Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForApproval\": \"All documents verified and meet requirements\",\n  \"notes\": \"Contractor has excellent credentials and portfolio\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/approve", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "approve"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Verification approved successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.verificationRequest.status).to.equal('approved');", "});"]}}]}, {"name": "Reject Verification Request", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reasonForRejection\": \"Incomplete documentation - missing PAN card verification\",\n  \"notes\": \"Please resubmit with complete documentation\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/reject", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "reject"]}}}, {"name": "Update Verification Priority", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}/priority", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}", "priority"]}}}, {"name": "Get Verification Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["verifications", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}}]}, {"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&limit=10&role=contractor", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "contractor"}]}}}, {"name": "Update User Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": false,\n  \"reason\": \"Suspended due to policy violation\"\n}"}, "url": {"raw": "{{baseUrl}}/users/{{user_id}}/status", "host": ["{{baseUrl}}"], "path": ["users", "{{user_id}}", "status"]}}}, {"name": "Get User Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/users/stats?timeframe=30d", "host": ["{{baseUrl}}"], "path": ["users", "stats"], "query": [{"key": "timeframe", "value": "30d"}]}}}]}, {"name": "Site Management", "item": [{"name": "Get All Sites", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/sites?page=1&limit=10&status=pending", "host": ["{{baseUrl}}"], "path": ["sites"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "pending"}]}}}, {"name": "Update Site Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"reason\": \"All documents verified and site meets requirements\"\n}"}, "url": {"raw": "{{baseUrl}}/sites/{{site_id}}/status", "host": ["{{baseUrl}}"], "path": ["sites", "{{site_id}}", "status"]}}}]}, {"name": "Service Management", "item": [{"name": "Get Transactions", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/transactions?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["transactions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Send Broadcast Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"System Maintenance Notice\",\n  \"message\": \"The system will be under maintenance from 2 AM to 4 AM tomorrow. Please plan accordingly.\",\n  \"type\": \"maintenance\",\n  \"targetAudience\": \"all\"\n}"}, "url": {"raw": "{{baseUrl}}/notifications/broadcast", "host": ["{{baseUrl}}"], "path": ["notifications", "broadcast"]}}}, {"name": "Get Support Tickets", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/support/tickets?page=1&limit=10&status=open", "host": ["{{baseUrl}}"], "path": ["support", "tickets"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "open"}]}}}]}]}