{"info": {"name": "Admin Management Service API", "description": "Complete API testing collection for Admin Management Service", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001/admin-service/api/v1", "type": "string"}, {"key": "jwt_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "type": "string"}, {"key": "session_id", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk", "type": "string"}, {"key": "verification_id", "value": "689421a8575779a37874d557", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Collection-level pre-request script", "// Set JWT token if needed", "if (!pm.environment.get('jwt_token')) {", "    console.log('Warning: JWT token not set. Please set jwt_token environment variable.');", "}"]}}], "item": [{"name": "1. Get Admin Dashboard Data", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has dashboard structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "    pm.expect(jsonData).to.have.property('approvedRequests');", "    pm.expect(jsonData).to.have.property('rejectedRequests');", "    pm.expect(jsonData).to.have.property('recentActivity');", "    pm.expect(jsonData.recentActivity).to.be.an('array');", "});", "", "pm.test('Legacy ratings field exists', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('ratings');", "    pm.expect(jsonData.ratings).to.have.property('name', 'rating');", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"name": "2. Get Verification Requests - De<PERSON>ult", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications", "host": ["{{baseUrl}}"], "path": ["verifications"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has required structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('verificationRequests');", "    pm.expect(jsonData).to.have.property('pagination');", "    pm.expect(jsonData.pagination).to.have.property('currentPage');", "    pm.expect(jsonData.pagination).to.have.property('totalPages');", "    pm.expect(jsonData.pagination).to.have.property('totalCount');", "});", "", "pm.test('Verification requests is an array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.verificationRequests).to.be.an('array');", "});", "", "// Store first verification ID for later use", "pm.test('Store verification ID if available', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.verificationRequests.length > 0) {", "        pm.environment.set('verification_id', jsonData.verificationRequests[0]._id);", "    }", "});"]}}]}, {"name": "3. Get Verification Requests - With Filters", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications?status=pending&type=contractor&limit=5&page=1&sortBy=createdAt&order=desc", "host": ["{{baseUrl}}"], "path": ["verifications"], "query": [{"key": "status", "value": "pending"}, {"key": "type", "value": "contractor"}, {"key": "limit", "value": "5"}, {"key": "page", "value": "1"}, {"key": "sortBy", "value": "createdAt"}, {"key": "order", "value": "desc"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filters applied correctly', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.equal(5);", "    pm.expect(jsonData.pagination.currentPage).to.equal(1);", "});", "", "pm.test('Verification requests match filter criteria', function () {", "    const jsonData = pm.response.json();", "    jsonData.verificationRequests.forEach(request => {", "        if (request.status) pm.expect(request.status).to.equal('pending');", "        if (request.type) pm.expect(request.type).to.equal('contractor');", "    });", "});"]}}]}, {"name": "4. Get Single Verification Request", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/{{verification_id}}", "host": ["{{baseUrl}}"], "path": ["verifications", "{{verification_id}}"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Response has verification request', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('verificationRequest');", "        pm.expect(jsonData.verificationRequest).to.have.property('_id');", "        pm.expect(jsonData.verificationRequest).to.have.property('type');", "        pm.expect(jsonData.verificationRequest).to.have.property('status');", "    });", "}", "", "if (pm.response.code === 404) {", "    pm.test('404 response has error message', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}"]}}]}, {"name": "5. Test Unauthorized Access", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "url": {"raw": "{{baseUrl}}/verifications", "host": ["{{baseUrl}}"], "path": ["verifications"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 401 (Unauthorized)', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"]}}]}, {"name": "6. Test Invalid Verification ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications/invalid_id_123", "host": ["{{baseUrl}}"], "path": ["verifications", "invalid_id_123"]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 404 or 500', function () {", "    pm.expect(pm.response.code).to.be.oneOf([404, 500]);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"]}}]}, {"name": "7. Test Invalid Query Parameters", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "url": {"raw": "{{baseUrl}}/verifications?status=invalid_status&type=invalid_type&limit=-1&page=0", "host": ["{{baseUrl}}"], "path": ["verifications"], "query": [{"key": "status", "value": "invalid_status"}, {"key": "type", "value": "invalid_type"}, {"key": "limit", "value": "-1"}, {"key": "page", "value": "0"}]}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200 (service handles invalid params gracefully)', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Service applies default values for invalid params', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.be.at.least(1);", "    pm.expect(jsonData.pagination.currentPage).to.be.at.least(1);", "});"]}}]}]}