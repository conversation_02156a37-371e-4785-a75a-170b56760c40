{"id": "admin-management-env", "name": "Admin Management Service Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3001/admin-service/api/v1", "type": "default", "enabled": true}, {"key": "jwt_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1NDMyMzUsImV4cCI6MTc1NDU3OTIzNX0.12Uldfibk3BL1RpB1tKtJxG0LHgPwMl03auP0XC9bhQ", "type": "secret", "enabled": true}, {"key": "session_id", "value": "tnnijVibRuu7iNYCeVyFVKveEzWDWxqEESTOBc4twL0", "type": "secret", "enabled": true}, {"key": "verification_id", "value": "689421a8575779a37874d557", "type": "default", "enabled": true}, {"key": "host", "value": "localhost", "type": "default", "enabled": true}, {"key": "port", "value": "3001", "type": "default", "enabled": true}, {"key": "service_path", "value": "/admin-service/api/v1", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}