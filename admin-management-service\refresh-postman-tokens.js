const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const USER_SERVICE_URL = 'http://localhost:3007/user-service/api/v1';
const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'Build@123'
};

async function refreshTokensForPostman() {
    try {
        console.log('🔄 Refreshing authentication tokens for Postman...');
        
        // Step 1: Authenticate
        const response = await fetch(`${USER_SERVICE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ADMIN_CREDENTIALS)
        });

        if (!response.ok) {
            throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
        }

        const authData = await response.json();
        
        if (!authData.accessToken || !authData.sessionId) {
            throw new Error('Authentication response missing required tokens');
        }

        console.log('✅ New tokens obtained successfully!');
        console.log(`   Access Token: ${authData.accessToken.substring(0, 30)}...`);
        console.log(`   Session ID: ${authData.sessionId.substring(0, 30)}...`);
        
        // Step 2: Update Postman collection
        const collectionPath = path.join(__dirname, 'postman-collection-complete.json');
        const environmentPath = path.join(__dirname, 'postman-environment.json');
        
        // Update collection variables
        if (fs.existsSync(collectionPath)) {
            const collection = JSON.parse(fs.readFileSync(collectionPath, 'utf8'));
            
            // Update collection variables
            collection.variable.forEach(variable => {
                if (variable.key === 'jwt_token') {
                    variable.value = authData.accessToken;
                }
                if (variable.key === 'session_id') {
                    variable.value = authData.sessionId;
                }
            });
            
            fs.writeFileSync(collectionPath, JSON.stringify(collection, null, 2));
            console.log('✅ Updated postman-collection-complete.json');
        }
        
        // Update environment variables
        if (fs.existsSync(environmentPath)) {
            const environment = JSON.parse(fs.readFileSync(environmentPath, 'utf8'));
            
            // Update environment values
            environment.values.forEach(value => {
                if (value.key === 'jwt_token') {
                    value.value = authData.accessToken;
                }
                if (value.key === 'session_id') {
                    value.value = authData.sessionId;
                }
            });
            
            fs.writeFileSync(environmentPath, JSON.stringify(environment, null, 2));
            console.log('✅ Updated postman-environment.json');
        }
        
        // Step 3: Test a sample request to verify tokens work
        console.log('\n🧪 Testing tokens with sample request...');
        const testResponse = await fetch('http://localhost:3001/admin-service/api/v1/', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authData.accessToken}`,
                'Session': authData.sessionId
            }
        });
        
        if (testResponse.ok) {
            console.log('✅ Token validation successful! Ready for Postman testing.');
        } else {
            console.log(`⚠️ Token validation returned ${testResponse.status}. Check admin service.`);
        }
        
        // Step 4: Display instructions
        console.log('\n📋 NEXT STEPS FOR POSTMAN EXTENSION:');
        console.log('=====================================');
        console.log('1. Open VS Code Postman extension');
        console.log('2. Import postman-collection-complete.json');
        console.log('3. Import postman-environment.json');
        console.log('4. Select "Admin Management Service Environment"');
        console.log('5. Start testing individual requests or run collection');
        console.log('\n🎯 All tokens are now fresh and ready for testing!');
        
        return {
            accessToken: authData.accessToken,
            sessionId: authData.sessionId,
            success: true
        };
        
    } catch (error) {
        console.error('❌ Failed to refresh tokens:', error.message);
        console.log('\n🔧 TROUBLESHOOTING:');
        console.log('- Ensure user service is running on port 3007');
        console.log('- Check admin credentials are correct');
        console.log('- Verify network connectivity');
        
        return {
            success: false,
            error: error.message
        };
    }
}

// Run the token refresh
if (require.main === module) {
    refreshTokensForPostman().catch(console.error);
}

module.exports = refreshTokensForPostman;
