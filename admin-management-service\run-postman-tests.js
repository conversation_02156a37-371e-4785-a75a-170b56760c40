#!/usr/bin/env node

/**
 * Admin Management Service - Postman Test Runner
 * 
 * This script helps you run the Postman collection tests programmatically
 * Make sure you have newman installed: npm install -g newman
 */

const { exec } = require('child_process');
const path = require('path');

// Configuration
const config = {
    collection: path.join(__dirname, 'postman-collection.json'),
    environment: path.join(__dirname, 'postman-environment.json'),
    reporters: ['cli', 'json'],
    outputFile: path.join(__dirname, 'test-results.json')
};

console.log('🚀 Admin Management Service - API Test Runner');
console.log('===============================================');

// Check if newman is installed
exec('newman --version', (error) => {
    if (error) {
        console.log('❌ Newman is not installed. Installing newman...');
        console.log('Run: npm install -g newman');
        console.log('Or use npx: npx newman run postman-collection.json -e postman-environment.json');
        return;
    }

    console.log('✅ Newman is available');
    runTests();
});

function runTests() {
    console.log('📋 Running API tests...');
    console.log(`Collection: ${config.collection}`);
    console.log(`Environment: ${config.environment}`);
    console.log('');

    const command = `newman run "${config.collection}" -e "${config.environment}" --reporters ${config.reporters.join(',')} --reporter-json-export "${config.outputFile}" --color on`;

    const testProcess = exec(command, (error, stdout, stderr) => {
        if (error) {
            console.log('❌ Test execution failed:');
            console.log(error.message);
            return;
        }

        if (stderr) {
            console.log('⚠️ Warnings:');
            console.log(stderr);
        }

        console.log('✅ Tests completed successfully!');
        console.log(`📊 Results saved to: ${config.outputFile}`);
    });

    testProcess.stdout.on('data', (data) => {
        process.stdout.write(data);
    });

    testProcess.stderr.on('data', (data) => {
        process.stderr.write(data);
    });
}

// Handle script arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node run-postman-tests.js [options]');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h     Show this help message');
    console.log('');
    console.log('Prerequisites:');
    console.log('  1. Start the admin-management-service (npm start)');
    console.log('  2. Set JWT token in postman-environment.json');
    console.log('  3. Ensure MongoDB and Redis are running');
    console.log('');
    console.log('Manual run:');
    console.log('  newman run postman-collection.json -e postman-environment.json');
    process.exit(0);
}
