const mongoose = require('mongoose');
const VerificationRequests = require('./model/verificationRequests');
require('dotenv').config();

const DATABASE_URL = process.env.DATABASE_URL || 'mongodb://localhost:27017/admin_management_service';

async function seedData() {
    try {
        await mongoose.connect(DATABASE_URL);
        console.log('Connected to MongoDB');

        // Clear existing verification requests
        await VerificationRequests.deleteMany({});
        console.log('Cleared existing verification requests');

        // Create sample verification requests
        const sampleRequests = [
            {
                _id: new mongoose.Types.ObjectId('689421a8575779a37874d557'),
                type: 'contractor',
                requesterId: 'user123',
                status: 'pending',
                priority: 'medium',
                entityData: {
                    name: '<PERSON>',
                    email: '<EMAIL>',
                    phone: '+1234567890',
                    experience: 5,
                    specialization: 'Electrical Work'
                }
            },
            {
                type: 'broker',
                requesterId: 'user456',
                status: 'pending',
                priority: 'high',
                entityData: {
                    name: '<PERSON>',
                    email: '<EMAIL>',
                    phone: '+0987654321',
                    experience: 8,
                    serviceAreas: ['New York', 'New Jersey']
                }
            },
            {
                type: 'site',
                requesterId: 'user789',
                status: 'pending',
                priority: 'low',
                entityData: {
                    name: 'Construction Site Alpha',
                    location: 'Downtown Area',
                    projectType: 'Commercial Building',
                    estimatedValue: 500000
                }
            },
            {
                type: 'contractor',
                requesterId: 'user101',
                status: 'approved',
                verifiedBy: new mongoose.Types.ObjectId(),
                reasonForApproval: 'All documents verified and credentials are excellent',
                processedAt: new Date(),
                entityData: {
                    name: 'Mike Johnson',
                    email: '<EMAIL>',
                    specialization: 'Plumbing'
                }
            },
            {
                type: 'broker',
                requesterId: 'user202',
                status: 'rejected',
                verifiedBy: new mongoose.Types.ObjectId(),
                reasonForRejection: 'Incomplete documentation - missing license verification',
                processedAt: new Date(),
                entityData: {
                    name: 'Sarah Wilson',
                    email: '<EMAIL>'
                }
            }
        ];

        const insertedRequests = await VerificationRequests.insertMany(sampleRequests);
        console.log(`Inserted ${insertedRequests.length} verification requests`);

        // Display the inserted data
        console.log('\nInserted verification requests:');
        insertedRequests.forEach(request => {
            console.log(`- ID: ${request._id}, Type: ${request.type}, Status: ${request.status}`);
        });

        console.log('\nSeed data created successfully!');
        console.log(`Test verification ID: ${insertedRequests[0]._id}`);
        
    } catch (error) {
        console.error('Error seeding data:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

// Run the seed function
if (require.main === module) {
    seedData();
}

module.exports = seedData;
