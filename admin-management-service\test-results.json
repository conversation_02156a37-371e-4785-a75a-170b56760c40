{"collection": {"item": [{"id": "4d617416-71c9-4446-849b-74dbecaf7fcb", "name": "1. Get Admin Dashboard Data", "request": {"url": {"path": [""], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "dc3c52d4-dde0-448d-b019-5a93c7b19995", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has dashboard structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "    pm.expect(jsonData).to.have.property('approvedRequests');", "    pm.expect(jsonData).to.have.property('rejectedRequests');", "    pm.expect(jsonData).to.have.property('recentActivity');", "    pm.expect(jsonData.recentActivity).to.be.an('array');", "});", "", "pm.test('Legacy ratings field exists', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('ratings');", "    pm.expect(jsonData.ratings).to.have.property('name', 'rating');", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "_lastExecutionId": "2f567718-be75-4d71-9c3d-0d995cc4a1af"}}]}, {"id": "a74678f4-ef9e-4a4a-925d-2bf36ef3bccb", "name": "2. Get Verification Requests - De<PERSON>ult", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "7528f25b-918c-4c86-81b3-566acf9ed2b3", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has required structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('verificationRequests');", "    pm.expect(jsonData).to.have.property('pagination');", "    pm.expect(jsonData.pagination).to.have.property('currentPage');", "    pm.expect(jsonData.pagination).to.have.property('totalPages');", "    pm.expect(jsonData.pagination).to.have.property('totalCount');", "});", "", "pm.test('Verification requests is an array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.verificationRequests).to.be.an('array');", "});", "", "// Store first verification ID for later use", "pm.test('Store verification ID if available', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.verificationRequests.length > 0) {", "        pm.environment.set('verification_id', jsonData.verificationRequests[0]._id);", "    }", "});"], "_lastExecutionId": "9d2b80d3-6ee9-4b8e-9b58-e4933f1b9cd2"}}]}, {"id": "61cb9ac0-fc6e-4806-9079-0c1de4abfd1a", "name": "3. Get Verification Requests - With Filters", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [{"key": "status", "value": "pending"}, {"key": "type", "value": "contractor"}, {"key": "limit", "value": "5"}, {"key": "page", "value": "1"}, {"key": "sortBy", "value": "createdAt"}, {"key": "order", "value": "desc"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "1e546b3e-7520-4c05-bdfd-cd5bbc001723", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filters applied correctly', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.equal(5);", "    pm.expect(jsonData.pagination.currentPage).to.equal(1);", "});", "", "pm.test('Verification requests match filter criteria', function () {", "    const jsonData = pm.response.json();", "    jsonData.verificationRequests.forEach(request => {", "        if (request.status) pm.expect(request.status).to.equal('pending');", "        if (request.type) pm.expect(request.type).to.equal('contractor');", "    });", "});"], "_lastExecutionId": "8fc5caf3-64db-4b79-a717-fff2902e11fc"}}]}, {"id": "cd8ac585-b773-494a-9151-78c6fb855d32", "name": "4. Get Single Verification Request", "request": {"url": {"path": ["verifications", "{{verification_id}}"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "eb29c38b-b533-4d51-8af9-2ec89b6dd089", "type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Response has verification request', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('verificationRequest');", "        pm.expect(jsonData.verificationRequest).to.have.property('_id');", "        pm.expect(jsonData.verificationRequest).to.have.property('type');", "        pm.expect(jsonData.verificationRequest).to.have.property('status');", "    });", "}", "", "if (pm.response.code === 404) {", "    pm.test('404 response has error message', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}"], "_lastExecutionId": "1a9884bf-5ec7-4a19-8da4-31f7f3607312"}}]}, {"id": "47218acb-d648-448a-a41b-3ec974de02d9", "name": "5. Test Unauthorized Access", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET", "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}}, "response": [], "event": [{"listen": "test", "script": {"id": "bb08afcb-bbbf-4d84-b986-5343590098b4", "type": "text/javascript", "exec": ["pm.test('Status code is 401 (Unauthorized)', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "_lastExecutionId": "83b3626b-4591-459b-8302-c52c309ce240"}}]}, {"id": "b6df0cba-9542-4bbf-917d-40bd15ce13e1", "name": "6. Test Invalid Verification ID", "request": {"url": {"path": ["verifications", "invalid_id_123"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "1dfbfaaa-c16c-4086-98e7-939212e09c9a", "type": "text/javascript", "exec": ["pm.test('Status code is 404 or 500', function () {", "    pm.expect(pm.response.code).to.be.oneOf([404, 500]);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "_lastExecutionId": "e8f79255-0fa7-4340-9b2c-9c972fbae2d4"}}]}, {"id": "074f04ae-9b4d-40d9-8968-5053de5e3233", "name": "7. Test Invalid Query Parameters", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [{"key": "status", "value": "invalid_status"}, {"key": "type", "value": "invalid_type"}, {"key": "limit", "value": "-1"}, {"key": "page", "value": "0"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "56f2a55d-bfa8-41f2-a408-c7fa329327e4", "type": "text/javascript", "exec": ["pm.test('Status code is 200 (service handles invalid params gracefully)', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Service applies default values for invalid params', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.be.at.least(1);", "    pm.expect(jsonData.pagination.currentPage).to.be.at.least(1);", "});"], "_lastExecutionId": "3f7f7185-6371-4aa3-8ddc-50759a79a722"}}]}], "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "{{jwt_token}}", "key": "token"}]}, "event": [{"listen": "prerequest", "script": {"id": "ad09c865-86c2-43c7-9902-eef07a3c86a4", "type": "text/javascript", "exec": ["// Collection-level pre-request script", "// Set JWT token if needed", "if (!pm.environment.get('jwt_token')) {", "    console.log('Warning: JWT token not set. Please set jwt_token environment variable.');", "}"], "_lastExecutionId": "034c887d-34c5-4c46-9f71-0a860156f764"}}], "variable": [{"type": "string", "value": "http://localhost:3001/admin-service/api/v1", "key": "baseUrl"}, {"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "jwt_token"}, {"type": "string", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk", "key": "session_id"}, {"type": "string", "value": "689421a8575779a37874d557", "key": "verification_id"}], "info": {"_postman_id": "03b1dfd8-17fb-4283-8901-d89b5f26e3ec", "name": "Admin Management Service API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": {"content": "Complete API testing collection for Admin Management Service", "type": "text/plain"}}}, "environment": {"_": {"postman_variable_scope": "environment"}, "id": "admin-management-env", "name": "Admin Management Service Environment", "values": [{"type": "any", "value": "http://localhost:3001/admin-service/api/v1", "key": "baseUrl"}, {"type": "any", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "jwt_token"}, {"type": "any", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk", "key": "session_id"}, {"type": "any", "value": "689421a8575779a37874d557", "key": "verification_id"}, {"type": "any", "value": "localhost", "key": "host"}, {"type": "any", "value": "3001", "key": "port"}, {"type": "any", "value": "/admin-service/api/v1", "key": "service_path"}]}, "globals": {"id": "b0d07f70-621d-406f-9016-0fae70ed9154", "values": []}, "run": {"stats": {"iterations": {"total": 1, "pending": 0, "failed": 0}, "items": {"total": 7, "pending": 0, "failed": 0}, "scripts": {"total": 14, "pending": 0, "failed": 0}, "prerequests": {"total": 7, "pending": 0, "failed": 0}, "requests": {"total": 7, "pending": 0, "failed": 0}, "tests": {"total": 7, "pending": 0, "failed": 0}, "assertions": {"total": 19, "pending": 0, "failed": 0}, "testScripts": {"total": 7, "pending": 0, "failed": 0}, "prerequestScripts": {"total": 7, "pending": 0, "failed": 0}}, "timings": {"responseAverage": 20.142857142857142, "responseMin": 6, "responseMax": 57, "responseSd": 17.166650155199772, "dnsAverage": 0, "dnsMin": 0, "dnsMax": 0, "dnsSd": 0, "firstByteAverage": 0, "firstByteMin": 0, "firstByteMax": 0, "firstByteSd": 0, "started": 1754538578534, "completed": 1754538579342}, "executions": [{"cursor": {"position": 0, "iteration": 0, "length": 7, "cycles": 1, "empty": false, "eof": false, "bof": true, "cr": false, "ref": "37af2a4e-b13c-455c-8cfe-e27201a9a37f", "httpRequestId": "fb2f73ed-26b3-4a59-9765-c983b3fe1f2f"}, "item": {"id": "4d617416-71c9-4446-849b-74dbecaf7fcb", "name": "1. Get Admin Dashboard Data", "request": {"url": {"path": [""], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "dc3c52d4-dde0-448d-b019-5a93c7b19995", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has dashboard structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('totalVerificationRequests');", "    pm.expect(jsonData).to.have.property('pendingRequests');", "    pm.expect(jsonData).to.have.property('approvedRequests');", "    pm.expect(jsonData).to.have.property('rejectedRequests');", "    pm.expect(jsonData).to.have.property('recentActivity');", "    pm.expect(jsonData.recentActivity).to.be.an('array');", "});", "", "pm.test('Legacy ratings field exists', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('ratings');", "    pm.expect(jsonData.ratings).to.have.property('name', 'rating');", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "_lastExecutionId": "2f567718-be75-4d71-9c3d-0d995cc4a1af"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", ""], "host": ["localhost"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "8c0d9a12-37ad-4b11-8efb-25da89457ac6", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "b44d0861-f96e-424d-aef9-286f2ddb1083", "status": "OK", "code": 200, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "141"}, {"key": "ETag", "value": "W/\"8d-cp5iqGtlulOSsFtG4pSE92uxMrg\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:38 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 116, 111, 116, 97, 108, 86, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 48, 44, 34, 112, 101, 110, 100, 105, 110, 103, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 48, 44, 34, 97, 112, 112, 114, 111, 118, 101, 100, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 48, 44, 34, 114, 101, 106, 101, 99, 116, 101, 100, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 48, 44, 34, 114, 101, 99, 101, 110, 116, 65, 99, 116, 105, 118, 105, 116, 121, 34, 58, 91, 93, 44, 34, 114, 97, 116, 105, 110, 103, 115, 34, 58, 123, 34, 110, 97, 109, 101, 34, 58, 34, 114, 97, 116, 105, 110, 103, 34, 125, 125]}, "cookie": [], "responseTime": 57, "responseSize": 141}, "id": "4d617416-71c9-4446-849b-74dbecaf7fcb", "assertions": [{"assertion": "Status code is 200", "skipped": false}, {"assertion": "Response has dashboard structure", "skipped": false}, {"assertion": "Legacy ratings field exists", "skipped": false}, {"assertion": "Response time is less than 2000ms", "skipped": false}]}, {"cursor": {"ref": "164f3d3b-1fed-43fe-9cb5-f5f3c8d6b34b", "length": 7, "cycles": 1, "position": 1, "iteration": 0, "httpRequestId": "a8cff52d-231e-430f-9c90-f1d7aef339e3"}, "item": {"id": "a74678f4-ef9e-4a4a-925d-2bf36ef3bccb", "name": "2. Get Verification Requests - De<PERSON>ult", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "7528f25b-918c-4c86-81b3-566acf9ed2b3", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has required structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('verificationRequests');", "    pm.expect(jsonData).to.have.property('pagination');", "    pm.expect(jsonData.pagination).to.have.property('currentPage');", "    pm.expect(jsonData.pagination).to.have.property('totalPages');", "    pm.expect(jsonData.pagination).to.have.property('totalCount');", "});", "", "pm.test('Verification requests is an array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.verificationRequests).to.be.an('array');", "});", "", "// Store first verification ID for later use", "pm.test('Store verification ID if available', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.verificationRequests.length > 0) {", "        pm.environment.set('verification_id', jsonData.verificationRequests[0]._id);", "    }", "});"], "_lastExecutionId": "9d2b80d3-6ee9-4b8e-9b58-e4933f1b9cd2"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications"], "host": ["localhost"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "b64398db-5265-4a95-bc6c-1d52e4e6f43f", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "90bc4c69-f4a1-42f2-b109-7fe13a5dbb44", "status": "OK", "code": 200, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "303"}, {"key": "ETag", "value": "W/\"12f-V9KnsC54hfC6q9rD3leiiCd22CU\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:38 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 118, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 91, 123, 34, 95, 105, 100, 34, 58, 34, 54, 56, 57, 52, 50, 49, 97, 56, 53, 55, 53, 55, 55, 57, 97, 51, 55, 56, 55, 52, 100, 53, 53, 55, 34, 44, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 110, 116, 114, 97, 99, 116, 111, 114, 34, 44, 34, 114, 101, 113, 117, 101, 115, 116, 101, 114, 73, 100, 34, 58, 34, 54, 55, 52, 97, 49, 98, 50, 99, 51, 100, 52, 101, 53, 102, 54, 55, 56, 57, 48, 49, 50, 51, 52, 53, 34, 44, 34, 115, 116, 97, 116, 117, 115, 34, 58, 34, 112, 101, 110, 100, 105, 110, 103, 34, 44, 34, 118, 97, 114, 105, 102, 105, 101, 100, 66, 121, 34, 58, 110, 117, 108, 108, 44, 34, 114, 101, 97, 115, 111, 110, 70, 111, 114, 82, 101, 106, 101, 99, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 95, 95, 118, 34, 58, 48, 125, 93, 44, 34, 112, 97, 103, 105, 110, 97, 116, 105, 111, 110, 34, 58, 123, 34, 99, 117, 114, 114, 101, 110, 116, 80, 97, 103, 101, 34, 58, 49, 44, 34, 116, 111, 116, 97, 108, 80, 97, 103, 101, 115, 34, 58, 49, 44, 34, 116, 111, 116, 97, 108, 67, 111, 117, 110, 116, 34, 58, 49, 44, 34, 108, 105, 109, 105, 116, 34, 58, 49, 48, 44, 34, 104, 97, 115, 78, 101, 120, 116, 80, 97, 103, 101, 34, 58, 102, 97, 108, 115, 101, 44, 34, 104, 97, 115, 80, 114, 101, 118, 80, 97, 103, 101, 34, 58, 102, 97, 108, 115, 101, 125, 125]}, "cookie": [], "responseTime": 28, "responseSize": 303}, "id": "a74678f4-ef9e-4a4a-925d-2bf36ef3bccb", "assertions": [{"assertion": "Status code is 200", "skipped": false}, {"assertion": "Response has required structure", "skipped": false}, {"assertion": "Verification requests is an array", "skipped": false}, {"assertion": "Store verification ID if available", "skipped": false}]}, {"cursor": {"ref": "5dc44242-5e69-4621-9d0a-bfaeddb527d9", "length": 7, "cycles": 1, "position": 2, "iteration": 0, "httpRequestId": "7a4116ad-0eec-4f92-86ab-46bfacb9d96d"}, "item": {"id": "61cb9ac0-fc6e-4806-9079-0c1de4abfd1a", "name": "3. Get Verification Requests - With Filters", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [{"key": "status", "value": "pending"}, {"key": "type", "value": "contractor"}, {"key": "limit", "value": "5"}, {"key": "page", "value": "1"}, {"key": "sortBy", "value": "createdAt"}, {"key": "order", "value": "desc"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "1e546b3e-7520-4c05-bdfd-cd5bbc001723", "type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filters applied correctly', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.equal(5);", "    pm.expect(jsonData.pagination.currentPage).to.equal(1);", "});", "", "pm.test('Verification requests match filter criteria', function () {", "    const jsonData = pm.response.json();", "    jsonData.verificationRequests.forEach(request => {", "        if (request.status) pm.expect(request.status).to.equal('pending');", "        if (request.type) pm.expect(request.type).to.equal('contractor');", "    });", "});"], "_lastExecutionId": "8fc5caf3-64db-4b79-a717-fff2902e11fc"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications"], "host": ["localhost"], "query": [{"key": "status", "value": "pending"}, {"key": "type", "value": "contractor"}, {"key": "limit", "value": "5"}, {"key": "page", "value": "1"}, {"key": "sortBy", "value": "createdAt"}, {"key": "order", "value": "desc"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "f2a91564-ed46-4017-b3ea-2c664f9b5a50", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "330fc3fd-72fd-4f6a-bdd0-8c642b63d5e9", "status": "OK", "code": 200, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "302"}, {"key": "ETag", "value": "W/\"12e-Q5VdXXx29JTzOaPW0MO0qKhun+k\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:38 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 118, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 91, 123, 34, 95, 105, 100, 34, 58, 34, 54, 56, 57, 52, 50, 49, 97, 56, 53, 55, 53, 55, 55, 57, 97, 51, 55, 56, 55, 52, 100, 53, 53, 55, 34, 44, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 110, 116, 114, 97, 99, 116, 111, 114, 34, 44, 34, 114, 101, 113, 117, 101, 115, 116, 101, 114, 73, 100, 34, 58, 34, 54, 55, 52, 97, 49, 98, 50, 99, 51, 100, 52, 101, 53, 102, 54, 55, 56, 57, 48, 49, 50, 51, 52, 53, 34, 44, 34, 115, 116, 97, 116, 117, 115, 34, 58, 34, 112, 101, 110, 100, 105, 110, 103, 34, 44, 34, 118, 97, 114, 105, 102, 105, 101, 100, 66, 121, 34, 58, 110, 117, 108, 108, 44, 34, 114, 101, 97, 115, 111, 110, 70, 111, 114, 82, 101, 106, 101, 99, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 95, 95, 118, 34, 58, 48, 125, 93, 44, 34, 112, 97, 103, 105, 110, 97, 116, 105, 111, 110, 34, 58, 123, 34, 99, 117, 114, 114, 101, 110, 116, 80, 97, 103, 101, 34, 58, 49, 44, 34, 116, 111, 116, 97, 108, 80, 97, 103, 101, 115, 34, 58, 49, 44, 34, 116, 111, 116, 97, 108, 67, 111, 117, 110, 116, 34, 58, 49, 44, 34, 108, 105, 109, 105, 116, 34, 58, 53, 44, 34, 104, 97, 115, 78, 101, 120, 116, 80, 97, 103, 101, 34, 58, 102, 97, 108, 115, 101, 44, 34, 104, 97, 115, 80, 114, 101, 118, 80, 97, 103, 101, 34, 58, 102, 97, 108, 115, 101, 125, 125]}, "cookie": [], "responseTime": 25, "responseSize": 302}, "id": "61cb9ac0-fc6e-4806-9079-0c1de4abfd1a", "assertions": [{"assertion": "Status code is 200", "skipped": false}, {"assertion": "Filters applied correctly", "skipped": false}, {"assertion": "Verification requests match filter criteria", "skipped": false}]}, {"cursor": {"ref": "3156b845-db45-4e9d-ab34-36fd9681ee00", "length": 7, "cycles": 1, "position": 3, "iteration": 0, "httpRequestId": "89cd3746-2c09-4bfb-8739-0ca585eaf36d"}, "item": {"id": "cd8ac585-b773-494a-9151-78c6fb855d32", "name": "4. Get Single Verification Request", "request": {"url": {"path": ["verifications", "{{verification_id}}"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "eb29c38b-b533-4d51-8af9-2ec89b6dd089", "type": "text/javascript", "exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Response has verification request', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('verificationRequest');", "        pm.expect(jsonData.verificationRequest).to.have.property('_id');", "        pm.expect(jsonData.verificationRequest).to.have.property('type');", "        pm.expect(jsonData.verificationRequest).to.have.property('status');", "    });", "}", "", "if (pm.response.code === 404) {", "    pm.test('404 response has error message', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}"], "_lastExecutionId": "1a9884bf-5ec7-4a19-8da4-31f7f3607312"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications", "689421a8575779a37874d557"], "host": ["localhost"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "dd1566fd-819d-458e-9d1a-8f4980f99ae9", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "81e9cad2-bd7d-49c6-93d9-e808e9ab56ad", "status": "OK", "code": 200, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "188"}, {"key": "ETag", "value": "W/\"bc-bpAWAYwrk6oe/Rkl6UIkfNNWGpU\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:38 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 118, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 34, 58, 123, 34, 95, 105, 100, 34, 58, 34, 54, 56, 57, 52, 50, 49, 97, 56, 53, 55, 53, 55, 55, 57, 97, 51, 55, 56, 55, 52, 100, 53, 53, 55, 34, 44, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 110, 116, 114, 97, 99, 116, 111, 114, 34, 44, 34, 114, 101, 113, 117, 101, 115, 116, 101, 114, 73, 100, 34, 58, 34, 54, 55, 52, 97, 49, 98, 50, 99, 51, 100, 52, 101, 53, 102, 54, 55, 56, 57, 48, 49, 50, 51, 52, 53, 34, 44, 34, 115, 116, 97, 116, 117, 115, 34, 58, 34, 112, 101, 110, 100, 105, 110, 103, 34, 44, 34, 118, 97, 114, 105, 102, 105, 101, 100, 66, 121, 34, 58, 110, 117, 108, 108, 44, 34, 114, 101, 97, 115, 111, 110, 70, 111, 114, 82, 101, 106, 101, 99, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 95, 95, 118, 34, 58, 48, 125, 125]}, "cookie": [], "responseTime": 8, "responseSize": 188}, "id": "cd8ac585-b773-494a-9151-78c6fb855d32", "assertions": [{"assertion": "Status code is 200 or 404", "skipped": false}, {"assertion": "Response has verification request", "skipped": false}]}, {"cursor": {"ref": "5321622e-5a19-43c1-aa57-f51052c58d2b", "length": 7, "cycles": 1, "position": 4, "iteration": 0, "httpRequestId": "fcac47d6-c120-41f8-85ad-35904096298c"}, "item": {"id": "47218acb-d648-448a-a41b-3ec974de02d9", "name": "5. Test Unauthorized Access", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET", "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}}, "response": [], "event": [{"listen": "test", "script": {"id": "bb08afcb-bbbf-4d84-b986-5343590098b4", "type": "text/javascript", "exec": ["pm.test('Status code is 401 (Unauthorized)', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "_lastExecutionId": "83b3626b-4591-459b-8302-c52c309ce240"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications"], "host": ["localhost"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "7d8cbc82-b56a-4565-a99f-118c692d77c1", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}}, "response": {"id": "bccfed49-b384-470f-8adf-1ffed67b5d5c", "status": "Unauthorized", "code": 401, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "31"}, {"key": "ETag", "value": "W/\"1f-v7RFlcFG70JSeSC4T+7UMXbC9xc\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:39 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 109, 101, 115, 115, 97, 103, 101, 34, 58, 34, 78, 111, 116, 32, 65, 117, 116, 104, 101, 110, 116, 105, 99, 97, 116, 101, 100, 34, 125]}, "cookie": [], "responseTime": 8, "responseSize": 31}, "id": "47218acb-d648-448a-a41b-3ec974de02d9", "assertions": [{"assertion": "Status code is 401 (Unauthorized)", "skipped": false}, {"assertion": "Response has error message", "skipped": false}]}, {"cursor": {"ref": "49a62533-377a-42fd-986d-84ed39fcd330", "length": 7, "cycles": 1, "position": 5, "iteration": 0, "httpRequestId": "27cedeb6-1cfe-4242-9052-c1b0ce9c88df"}, "item": {"id": "b6df0cba-9542-4bbf-917d-40bd15ce13e1", "name": "6. Test Invalid Verification ID", "request": {"url": {"path": ["verifications", "invalid_id_123"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "1dfbfaaa-c16c-4086-98e7-939212e09c9a", "type": "text/javascript", "exec": ["pm.test('Status code is 404 or 500', function () {", "    pm.expect(pm.response.code).to.be.oneOf([404, 500]);", "});", "", "pm.test('Response has error message', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "_lastExecutionId": "e8f79255-0fa7-4340-9b2c-9c972fbae2d4"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications", "invalid_id_123"], "host": ["localhost"], "query": [], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "03f945f3-c2cc-44f9-a87e-7542d459170b", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "1e10a1ed-d9dc-4d9c-8e37-a9ae354c8edb", "status": "Internal Server Error", "code": 500, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "131"}, {"key": "ETag", "value": "W/\"83-YCTadNZGMlsksD57NSeIw/9aHVo\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:39 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 109, 101, 115, 115, 97, 103, 101, 34, 58, 34, 67, 97, 115, 116, 32, 116, 111, 32, 79, 98, 106, 101, 99, 116, 73, 100, 32, 102, 97, 105, 108, 101, 100, 32, 102, 111, 114, 32, 118, 97, 108, 117, 101, 32, 92, 34, 105, 110, 118, 97, 108, 105, 100, 95, 105, 100, 95, 49, 50, 51, 92, 34, 32, 40, 116, 121, 112, 101, 32, 115, 116, 114, 105, 110, 103, 41, 32, 97, 116, 32, 112, 97, 116, 104, 32, 92, 34, 95, 105, 100, 92, 34, 32, 102, 111, 114, 32, 109, 111, 100, 101, 108, 32, 92, 34, 86, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 115, 92, 34, 34, 125]}, "cookie": [], "responseTime": 6, "responseSize": 131}, "id": "b6df0cba-9542-4bbf-917d-40bd15ce13e1", "assertions": [{"assertion": "Status code is 404 or 500", "skipped": false}, {"assertion": "Response has error message", "skipped": false}]}, {"cursor": {"ref": "3b913873-f456-41e5-8a6b-38e6146defae", "length": 7, "cycles": 1, "position": 6, "iteration": 0, "httpRequestId": "642d1d53-8d71-4fd2-b5a7-fb59c257a2f3"}, "item": {"id": "074f04ae-9b4d-40d9-8968-5053de5e3233", "name": "7. Test Invalid Query Parameters", "request": {"url": {"path": ["verifications"], "host": ["{{baseUrl}}"], "query": [{"key": "status", "value": "invalid_status"}, {"key": "type", "value": "invalid_type"}, {"key": "limit", "value": "-1"}, {"key": "page", "value": "0"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "{{session_id}}"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "56f2a55d-bfa8-41f2-a408-c7fa329327e4", "type": "text/javascript", "exec": ["pm.test('Status code is 200 (service handles invalid params gracefully)', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Service applies default values for invalid params', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.pagination.limit).to.be.at.least(1);", "    pm.expect(jsonData.pagination.currentPage).to.be.at.least(1);", "});"], "_lastExecutionId": "3f7f7185-6371-4aa3-8ddc-50759a79a722"}}]}, "request": {"url": {"protocol": "http", "port": "3001", "path": ["admin-service", "api", "v1", "verifications"], "host": ["localhost"], "query": [{"key": "status", "value": "invalid_status"}, {"key": "type", "value": "invalid_type"}, {"key": "limit", "value": "-1"}, {"key": "page", "value": "0"}], "variable": []}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Session", "value": "iwtSAWrxFX3lsIbWIToUlki7CfE5_dEAqZBs3jfdVOk"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "system": true}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "system": true}, {"key": "Accept", "value": "*/*", "system": true}, {"key": "Cache-Control", "value": "no-cache", "system": true}, {"key": "Postman-To<PERSON>", "value": "2e51d499-e556-4bc8-bfb0-9a8b5f7d26e0", "system": true}, {"key": "Host", "value": "localhost:3001", "system": true}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "system": true}, {"key": "Connection", "value": "keep-alive", "system": true}], "method": "GET", "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NTQ1Mzg0MzksImV4cCI6MTc1NDU3NDQzOX0.cBqY3IfqyyGTs8J2pcCj4Pa385qa_PIF3gaPr5pH9p4", "key": "token"}]}}, "response": {"id": "f65fab00-c9b6-41cd-916e-efaeba672868", "status": "OK", "code": 200, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, PATCH, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "301"}, {"key": "ETag", "value": "W/\"12d-QGLZZTiJe8yqpkKuMW8gA1bOKJA\""}, {"key": "Date", "value": "Thu, 07 Aug 2025 03:49:39 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "stream": {"type": "<PERSON><PERSON><PERSON>", "data": [123, 34, 118, 101, 114, 105, 102, 105, 99, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 115, 34, 58, 91, 123, 34, 95, 105, 100, 34, 58, 34, 54, 56, 57, 52, 50, 49, 97, 56, 53, 55, 53, 55, 55, 57, 97, 51, 55, 56, 55, 52, 100, 53, 53, 55, 34, 44, 34, 116, 121, 112, 101, 34, 58, 34, 99, 111, 110, 116, 114, 97, 99, 116, 111, 114, 34, 44, 34, 114, 101, 113, 117, 101, 115, 116, 101, 114, 73, 100, 34, 58, 34, 54, 55, 52, 97, 49, 98, 50, 99, 51, 100, 52, 101, 53, 102, 54, 55, 56, 57, 48, 49, 50, 51, 52, 53, 34, 44, 34, 115, 116, 97, 116, 117, 115, 34, 58, 34, 112, 101, 110, 100, 105, 110, 103, 34, 44, 34, 118, 97, 114, 105, 102, 105, 101, 100, 66, 121, 34, 58, 110, 117, 108, 108, 44, 34, 114, 101, 97, 115, 111, 110, 70, 111, 114, 82, 101, 106, 101, 99, 116, 105, 111, 110, 34, 58, 34, 34, 44, 34, 95, 95, 118, 34, 58, 48, 125, 93, 44, 34, 112, 97, 103, 105, 110, 97, 116, 105, 111, 110, 34, 58, 123, 34, 99, 117, 114, 114, 101, 110, 116, 80, 97, 103, 101, 34, 58, 49, 44, 34, 116, 111, 116, 97, 108, 80, 97, 103, 101, 115, 34, 58, 51, 44, 34, 116, 111, 116, 97, 108, 67, 111, 117, 110, 116, 34, 58, 51, 44, 34, 108, 105, 109, 105, 116, 34, 58, 49, 44, 34, 104, 97, 115, 78, 101, 120, 116, 80, 97, 103, 101, 34, 58, 116, 114, 117, 101, 44, 34, 104, 97, 115, 80, 114, 101, 118, 80, 97, 103, 101, 34, 58, 102, 97, 108, 115, 101, 125, 125]}, "cookie": [], "responseTime": 9, "responseSize": 301}, "id": "074f04ae-9b4d-40d9-8968-5053de5e3233", "assertions": [{"assertion": "Status code is 200 (service handles invalid params gracefully)", "skipped": false}, {"assertion": "Service applies default values for invalid params", "skipped": false}]}], "transfers": {"responseTotal": 1397}, "failures": [], "error": null}}