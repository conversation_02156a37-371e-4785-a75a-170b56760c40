const express = require('express');
const mongoose = require('mongoose');
const cookieParser = require('cookie-parser');
const log = require('@build-connect/utils/log');
const { connectRedis } = require('./cache');

const { DATABASE_URL, PORT } = require('./config').getAll();

// routes
const routes = require('./routes/routes');

const app = express();

app.use(cookieParser());
app.use(express.json());

app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, PATCH, DELETE, OPTIONS'
    );
    res.setHeader(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization'
    );
    next();
});

app.use('/customer-service/api/v1', routes);

app.use((err, _req, res, _next) => {
    const { statusCode = 500, data } = err;
    log(err);
    if (data && data.length > 0) {
        res.status(statusCode).json({ message: data });
        return;
    }
    let { message } = err;
    if (!err.message) message = 'something went wrong, try again!';
    res.status(statusCode).json({ message });
});

async function main() {
    await mongoose.connect(DATABASE_URL);
    await connectRedis();

    // eslint-disable-next-line no-console
    app.listen(PORT, () => console.log(`App listening on port ${PORT}`));
}

main().catch((err) => console.error(err));
