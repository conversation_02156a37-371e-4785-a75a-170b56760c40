version: '3.8'

services:
    build-connect-kafka:
        image: bitnami/kafka:latest
        restart: always
        container_name: build-connect-kafka
        ports:
            - '9092:9092'
        environment:
            - KAFKA_ENABLE_KRAFT=yes
            - KAFKA_CFG_PROCESS_ROLES=broker,controller
            - KAFKA_CFG_NODE_ID=1
            - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@build-connect-kafka:9093
            - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
            - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
            - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT
            - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
            - ALLOW_PLAINTEXT_LISTENER=yes
        volumes:
            - kafka_data:/bitnami/kafka

    build-connect-mongodb:
        image: mongo:latest
        container_name: build-connect-mongodb
        ports:
            - '27017:27017'
        command: ['mongod', '--replSet', 'rs0', '--bind_ip_all']
        volumes:
            - mongo_data:/data/db
            - ./rs-init.js:/docker-entrypoint-initdb.d/rs-init.js:ro

    build-connect-cache:
        image: redis:latest
        container_name: build-connect-cache
        ports:
            - '6379:6379'
        volumes:
            - redis_data:/data

volumes:
    kafka_data:
    mongo_data:
    redis_data:
