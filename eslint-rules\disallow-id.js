// eslint-rules/disallow-id.js
export default {
    meta: {
        type: 'problem',
        docs: {
            description: 'Disallow "ID", enforce "Id"',
        },
        fixable: 'code',
        messages: {
            useId: 'Use "Id" instead of "ID".',
        },
    },

    create(context) {
        // Track top-level imported or required variables
        const importedIdentifiers = new Set();

        return {
            ImportDeclaration(node) {
                for (const specifier of node.specifiers) {
                    importedIdentifiers.add(specifier.local.name);
                }
            },

            VariableDeclarator(node) {
                // Track const { ClientIDs } = require(...) identifiers
                if (
                    node.init &&
                    node.init.type === 'CallExpression' &&
                    node.init.callee.name === 'require'
                ) {
                    if (node.id.type === 'Identifier') {
                        importedIdentifiers.add(node.id.name);
                    }

                    // Destructured import: const { A, B } = require(...)
                    if (node.id.type === 'ObjectPattern') {
                        for (const prop of node.id.properties) {
                            if (prop.type === 'Property' && prop.key?.name) {
                                importedIdentifiers.add(prop.value.name);
                            }
                        }
                    }
                }
            },

            Identifier(node) {
                const name = node.name;

                // Only match all-uppercase ID (not Id)
                if (!name.includes('ID')) return;

                // Skip if from import or require
                if (importedIdentifiers.has(name)) return;

                const parent = node.parent;

                // Skip mongoose.Schema.Types.ObjectId
                if (
                    parent.type === 'MemberExpression' &&
                    ['ObjectId', 'Schema', 'Types'].includes(
                        parent.property?.name
                    )
                ) {
                    return;
                }

                // Skip inside mongoose type definitions
                if (
                    parent.type === 'Property' &&
                    parent.value?.type === 'MemberExpression' &&
                    parent.value?.object?.name === 'mongoose'
                ) {
                    return;
                }

                context.report({
                    node,
                    messageId: 'useId',
                    fix(fixer) {
                        const fixed = name.replace(/ID/g, 'Id');
                        return fixer.replaceText(node, fixed);
                    },
                });
            },
        };
    },
};
