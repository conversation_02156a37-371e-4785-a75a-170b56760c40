// eslint.config.js
import js from '@eslint/js';
import prettier from 'eslint-config-prettier';
import prettierPlugin from 'eslint-plugin-prettier';
import globals from 'globals';
import disallowId from './eslint-rules/disallow-id.js';

/** @type {import("eslint").Linter.FlatConfig[]} */
export default [
    {
        ignores: [
            '**/node_modules/**',
            'dist',
            'build/**',
            'eslint.config.js',
            'eslint-rules/**.js',
            'api-gateway/**.js',
        ],
    },
    js.configs.recommended,
    {
        files: ['**/*.js'],
        languageOptions: {
            ecmaVersion: 'latest',
            sourceType: 'commonjs',
            globals: {
                ...globals.node,
            },
        },
        plugins: {
            custom: {
                rules: {
                    'disallow-id': disallowId,
                },
            },
            prettier: prettierPlugin,
        },
        rules: {
            'custom/disallow-id': 'error',
            'no-underscore-dangle': 'off',
            'no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
            'prefer-const': 'off',
            'func-names': ['warn', 'never'],
            'consistent-return': 'off',
            'linebreak-style': 'off',
            'no-console': ['error', { allow: ['error'] }],
            'prettier/prettier': 'error',
        },
    },
    prettier,
];
