const express = require('express');

const router = express.Router();

const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const { getRatings } = require('../controllers/getratings');
const { client } = require('../cache');

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, catchAsync(getRatings));

module.exports = router;
