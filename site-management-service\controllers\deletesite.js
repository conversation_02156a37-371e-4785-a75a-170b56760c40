const ExpressError = require('@build-connect/utils/ExpressError');
const { Cloudinary } = require('../cloudinary');
const Asset = require('../model/Asset');

exports.deleteAsset = async (req, res) => {
    const { siteId, assetId } = req.params;

    const asset = await Asset.findOneAndDelete({
        _id: assetId,
        entityId: siteId,
    });

    if (!asset) {
        throw new ExpressError('asset could not be found', 404);
    }
    const { result } = await Cloudinary.getCloudinary().uploader.destroy(
        asset.fileName
    );

    if (result !== 'ok' && result !== 'not found') {
        throw new ExpressError('something went wrong', 500);
    }

    return res.status(200).json({ message: 'asset deleted successfully' });
};
