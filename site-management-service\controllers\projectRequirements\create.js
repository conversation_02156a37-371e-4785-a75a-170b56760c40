const { withTransaction } = require('@build-connect/utils/transaction');
const Project = require('../../model/project');
const ProjectRequirement = require('../../model/projectRequirement');

exports.submitProjectWithRequirement = async (req, res) => {
    const userId = req.user.id;

    const {
        projectName,
        projectType,
        location,
        constructionType,
        expectedStartDate,
        expectedCompletionDate,
        budget,
        designPreferences,
        additionalFacilities,
        brokerAssistanceRequired,
        specialInstructions,
    } = req.body;

    const result = await withTransaction(async (session) => {
        const project = new Project({
            userId,
            projectName,
            status: 'Initiated',
        });
        await project.save({ session });

        const requirement = new ProjectRequirement({
            userId,
            projectType,
            location,
            constructionType,
            expectedStartDate,
            expectedCompletionDate,
            budget,
            designPreferences,
            additionalFacilities,
            brokerAssistanceRequired,
            specialInstructions,
            projectId: project._id,
        });
        await requirement.save({ session });

        return { project, requirement };
    });

    res.status(201).json({
        message: 'Project and requirement created successfully',
        projectId: result.project._id,
        requirementId: result.requirement._id,
    });
};
