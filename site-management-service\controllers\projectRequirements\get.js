const ExpressError = require('@build-connect/utils/ExpressError');
const Project = require('../../model/project');
const UserRequirement = require('../../model/projectRequirement');

exports.getProjectById = async (req, res) => {
    const { projectId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    const project = await Project.findById(projectId).lean();
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    // Check if user has access to this project
    const hasAccess =
        project.userId.toString() === userId || // Project owner
        (project.contractorId && project.contractorId.toString() === userId) || // Assigned contractor
        (project.brokerId && project.brokerId.toString() === userId); // Assigned broker

    if (!hasAccess) {
        throw new ExpressError('You do not have access to this project', 403);
    }

    // Determine user's role in this project
    let userRoleInProject = 'owner';
    if (project.contractorId && project.contractorId.toString() === userId) {
        userRoleInProject = 'contractor';
    } else if (project.brokerId && project.brokerId.toString() === userId) {
        userRoleInProject = 'broker';
    }

    const requirement = await UserRequirement.findOne({ projectId }).lean();
    if (!requirement) {
        throw new ExpressError('requirement not found', 404);
    }

    res.status(200).json({
        project: {
            ...project,
            userRoleInProject
        },
        requirement
    });
};

exports.getProjects = async (req, res) => {
    const userId = req.user.id;
    const userRole = req.user.role;

    let projectQuery;

    if (userRole === 'contractor') {
        // For contractors: show projects they own OR projects they're assigned to
        projectQuery = {
            $or: [
                { userId: userId },
                { contractorId: userId }
            ]
        };
    } else if (userRole === 'broker') {
        // For brokers: show projects they own OR projects they're assigned to
        projectQuery = {
            $or: [
                { userId: userId },
                { brokerId: userId }
            ]
        };
    } else {
        // For regular users: only show projects they own
        projectQuery = { userId: userId };
    }

    const projects = await Project.find(projectQuery).lean();

    // Get requirements for each project and merge the data
    const projectsWithRequirements = await Promise.all(
        projects.map(async (project) => {
            const requirement = await UserRequirement.findOne({ projectId: project._id }).lean();

            if (requirement) {
                // Determine user's role in this project
                let userRoleInProject = 'owner';
                if (project.contractorId && project.contractorId.toString() === userId) {
                    userRoleInProject = 'contractor';
                } else if (project.brokerId && project.brokerId.toString() === userId) {
                    userRoleInProject = 'broker';
                }

                // Merge project and requirement data
                return {
                    ...project,
                    userRoleInProject, // Add this field to indicate user's role
                    projectType: requirement.projectType,
                    constructionType: requirement.constructionType,
                    location: requirement.location,
                    budget: requirement.budget,
                    designPreferences: requirement.designPreferences,
                    additionalFacilities: requirement.additionalFacilities,
                    brokerAssistanceRequired: requirement.brokerAssistanceRequired,
                    specialInstructions: requirement.specialInstructions,
                    expectedStartDate: requirement.expectedStartDate,
                    expectedCompletionDate: requirement.expectedCompletionDate,
                };
            }

            // If no requirement found, return project with default values to prevent undefined errors
            return {
                ...project,
                projectType: 'Not specified',
                constructionType: 'Not specified',
                location: {
                    address: 'Not specified',
                    city: 'Not specified',
                    state: 'Not specified',
                    pincode: 'Not specified',
                    plotSizeSqFt: null
                },
                budget: {
                    minBudget: null,
                    maxBudget: null
                },
                designPreferences: {
                    floors: null,
                    bedrooms: null,
                    bathrooms: null,
                    parkingRequired: false,
                    gardenRequired: false,
                    vastuCompliance: false
                },
                additionalFacilities: [],
                brokerAssistanceRequired: false,
                specialInstructions: '',
                expectedStartDate: null,
                expectedCompletionDate: null,
            };
        })
    );

    res.status(200).json({ projects: projectsWithRequirements });
};
