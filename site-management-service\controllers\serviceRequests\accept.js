const { withTransaction } = require('@build-connect/utils/transaction');
const ExpressError = require('@build-connect/utils/ExpressError');
const ServiceRequest = require('../../model/serviceRequest');
const Project = require('../../model/project');

exports.updateServiceRequest = async (req, res) => {
    const userId = req.user.id;
    const userRole = req.user.role;
    const projectId = req.params.projectID;
    const { status } = req.body;

    await withTransaction(async (session) => {
        // Find the service request
        const serviceRequest = await ServiceRequest.findOne({
            recipientId: userId,
            projectId,
        }).session(session);

        if (!serviceRequest) {
            throw new ExpressError('Service request not found', 404);
        }

        // Find the project
        const project = await Project.findOne({ _id: projectId }).session(
            session
        );
        if (!project) {
            throw new ExpressError('Project not found', 404);
        }

        if (status === 'accepted') {
            let updated = false;
            if (userRole === 'broker' && !project.brokerId) {
                project.brokerId = userId;
                updated = true;
            } else if (userRole === 'contractor' && !project.contractorId) {
                project.contractorId = userId;
                updated = true;
            }

            if (updated) {
                await project.save({ session });

                // Delete all service requests for this project and role
                // We need to find all service requests for this project where the recipient has the same role
                // Since we can't directly query by role in ServiceRequest, we'll delete all for this project
                // and let the frontend handle role-specific filtering
                await ServiceRequest.deleteMany({
                    projectId,
                }).session(session);
            } else {
                // Just delete the current service request if not accepted or already filled
                await ServiceRequest.deleteOne({
                    recipientId: userId,
                    projectId,
                }).session(session);
            }
        } else {
            // Delete the rejected service request
            await ServiceRequest.deleteOne({
                recipientId: userId,
                projectId,
            }).session(session);
        }
    });

    res.status(200).json({
        message:
            status === 'accepted'
                ? 'Service request accepted successfully'
                : 'Service request rejected',
    });
};
