const mongoose = require('mongoose');

const Encumbrancecertificateschema = new mongoose.Schema(
    {
        entityId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: 'site',
        },
        encOwnerName: {
            type: String,
            required: true,
        },
        encDocumentNo: {
            type: String,
            required: true,
        },
        surveyNo: {
            type: String,
            required: true,
        },
        village: {
            type: String,
        },
        subDistrict: {
            type: String,
        },
        District: {
            type: String,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model(
    'Encumbrancecertificate',
    Encumbrancecertificateschema
);
