const mongoose = require('mongoose');

const projectRequirementSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        projectId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Project',
            required: true,
        },

        projectType: {
            type: String,
            enum: ['Residential', 'Commercial', 'Industrial', 'Other'],
            required: true,
        },

        location: {
            address: String,
            city: String,
            state: String,
            pincode: String,
            plotSizeSqFt: Number,
        },

        constructionType: {
            type: String,
            enum: ['New Construction', 'Renovation', 'Extension'],
            required: true,
        },

        expectedStartDate: Date,
        expectedCompletionDate: Date,

        budget: {
            minBudget: Number,
            maxBudget: Number,
        },

        designPreferences: {
            floors: Number,
            bedrooms: Number,
            bathrooms: Number,
            parkingRequired: Boolean,
            gardenRequired: Boolean,
            vastuCompliance: Boolean,
        },

        additionalFacilities: [String],

        brokerAssistanceRequired: {
            type: Boolean,
            default: false,
        },

        specialInstructions: String,
    },
    { timestamps: true }
);

module.exports = mongoose.model('projectRequirement', projectRequirementSchema);
