const mongoose = require('mongoose');

const Propertytaxreceiptschema = new mongoose.Schema(
    {
        entityId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'site',
        },
        ptrOwnerName: {
            type: String,
        },
        ptrReciptNo: {
            type: String,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model('Propertytaxreceipt', Propertytaxreceiptschema);
