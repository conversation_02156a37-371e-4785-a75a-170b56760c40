// models/site.js
const mongoose = require('mongoose');

mongoose.set('autoIndex', true);

const siteSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        addressLine1: {
            type: String,
            required: true,
        },
        addressLine2: {
            type: String,
        },
        landmark: {
            type: String,
        },
        location: {
            type: String,
            required: true,
        },
        pincode: {
            type: String,
            required: true,
        },
        state: {
            type: String,
            required: true,
        },
        district: {
            type: String,
            required: true,
        },
        plotArea: {
            type: Number,
            required: true,
            default: 0, // unit: squarefeet (you can add a note in comments or UI)
        },
        price: {
            type: Number,
            required: true, // curency in rupees
        },
        latitude: {
            type: Number,
            required: true,
        },
        longitude: {
            type: Number,
            required: true,
        },
        geo: {
            type: {
                type: String,
                enum: ['Point'],
                required: true,
                default: 'Point',
            },
            coordinates: {
                type: [Number], // [longitude, latitude]
                required: true,
            },
        },
        status: {
            type: String,
            enum: ['pending', 'approved', 'rejected'],
            default: 'pending',
        },
        verificationId: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },
        verifiedBy: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },
        reasonForRejection: {
            type: String,
            default: '',
        },
        totalInterestShown: {
            type: Number,
            default: 0,
        },

        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        brokerId: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },
        contractorId: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },
    },
    { timestamps: true }
);

// creating index
siteSchema.index(
    {
        name: 'text',
        addressLine1: 'text',
        addressLine2: 'text',
        landmark: 'text',
        location: 'text',
        state: 'text',
        district: 'text',
        pincode: 'text',
    },
    {
        name: 'site_text_search',
        default_language: 'none',
        weights: { name: 4, landmark: 10, location: 6 },
    }
);

siteSchema.index({ geo: '2dsphere' }, { name: 'site_geo_index' });

// sync geo lat and long
siteSchema.pre('validate', function (next) {
    if (
        this.isModified('latitude') ||
        this.isModified('longitude') ||
        !this.geo
    ) {
        this.geo = {
            type: 'Point',
            coordinates: [this.longitude, this.latitude],
        };
    }
    next();
});

module.exports = mongoose.model('Site', siteSchema);
