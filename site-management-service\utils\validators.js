const { body, param, query } = require('express-validator');

const REGEX_PATTERNS = {
    NAME: /^[a-zA-Z\s]+$/, // Only alphabets and spaces
    COORDINATE: /^-?\d+(\.\d+)?$/, // Validates latitude/longitude (e.g., -12.3456)
    PINCODE: /^\d{6}$/, // Exactly 6 digits
};

const mongoIdParam = (name = 'id') =>
    param(name)
        .isMongoId()
        .withMessage(`${name} must be a valid MongoDB ObjectId`);

const siteValidators = [
    body('name')
        .trim()
        .notEmpty()
        .withMessage('Site name is required')
        .isLength({ min: 5, max: 100 })
        .withMessage('Site name must be between 5 and 100 characters')
        .matches(REGEX_PATTERNS.NAME)
        .withMessage('Site name must contain only letters and spaces'),

    body('addressLine1')
        .trim()
        .notEmpty()
        .withMessage('Address Line 1 is required'),

    body('addressLine2').optional().trim(),

    body('landmark').optional().trim(),

    body('location').trim().notEmpty().withMessage('Location is required'),

    body('pincode')
        .trim()
        .notEmpty()
        .withMessage('Pincode is required')
        .matches(REGEX_PATTERNS.PINCODE)
        .withMessage('Invalid pincode format'),

    body('state').trim().notEmpty().withMessage('State is required'),

    body('district').trim().notEmpty().withMessage('District is required'),

    body('plotArea')
        .notEmpty()
        .withMessage('Plot area is required')
        .isFloat({ min: 0.1 })
        .withMessage('Plot area must be a positive number'),

    body('price')
        .notEmpty()
        .withMessage('Price is required')
        .isNumeric()
        .withMessage('Price must be a valid number'),

    body('latitude')
        .notEmpty()
        .withMessage('Latitude is required')
        .matches(REGEX_PATTERNS.COORDINATE)
        .withMessage('Invalid latitude'),

    body('longitude')
        .notEmpty()
        .withMessage('Longitude is required')
        .matches(REGEX_PATTERNS.COORDINATE)
        .withMessage('Invalid longitude'),

    // Property Document Fields
    body('encOwnerName')
        .trim()
        .notEmpty()
        .withMessage('Owner name is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Owner name must be between 2 and 100 characters')
        .matches(REGEX_PATTERNS.NAME)
        .withMessage('Owner name must contain only letters and spaces'),

    body('ptrOwnerName')
        .trim()
        .notEmpty()
        .withMessage('Owner name is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Owner name must be between 2 and 100 characters')
        .matches(REGEX_PATTERNS.NAME)
        .withMessage('Owner name must contain only letters and spaces'),

    body('encDocumentNo')
        .trim()
        .notEmpty()
        .withMessage('Encumbrance Certificate number is required')
        .matches(/^\d{1,6}\d{4}$/)
        .withMessage('Format must be <docNo>/<year>, e.g. 1234/2024')
        .custom((value) => {
            const [, yearPart] = value.split('/');
            const year = Number(yearPart);
            if (year < 1900 || year > new Date().getFullYear()) {
                throw new Error('Year in EC number is invalid');
            }
            return true;
        }),

    body('ptrReciptNo')
        .trim()
        .notEmpty()
        .withMessage('Tax-receipt number is required')
        .isLength({ min: 6, max: 30 })
        .withMessage('Tax-receipt number must be 6–30 characters long')
        .matches(
            /^([A-Z]{0,3}\d{1,3}|[A-Z]{2,5})[/-]\d{1,4}[/-]\d{4}(?:-[0-9]{2})?[/-]\d{3,6}$/i
        )
        .withMessage(
            'Expect something like ZN/013/2024-25/00123 (zone/division/bill/sub). ' +
                'Only letters, numbers, - or / allowed.'
        ),

    body('surveyNo')
        .trim()
        .notEmpty()
        .withMessage('Survey number is required')
        .isLength({ min: 1, max: 20 })
        .withMessage('Survey number must be between 1 and 20 characters')
        .matches(/^\d+[A-Z]?(?:\d+[A-Z]?)?$/i)
        .withMessage(
            'Survey number must look like 123, 123/1, or 123/1A (digits plus optional letter and /sub-number)'
        ),

    body('village')
        .optional({ checkFalsy: true })
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Village name must be 2–100 characters long')
        .matches(/^[A-Za-z\s.'-]+$/)
        .withMessage(
            'Village name can contain letters, spaces, apostrophes, periods and hyphens only'
        ),

    body('subDistrict')
        .optional({ checkFalsy: true })
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Sub-district name must be 2–100 characters long')
        .matches(/^[A-Za-z\s.'-]+$/)
        .withMessage(
            'Sub-district name can contain letters, spaces, apostrophes, periods and hyphens only'
        ),

    body('district')
        .optional({ checkFalsy: true })
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('District name must be 2–100 characters long')
        .matches(/^[A-Za-z\s.'-]+$/)
        .withMessage(
            'District name can contain letters, spaces, apostrophes, periods and hyphens only'
        ),
];

const ALLOWED_SORT = [
    'interestShown',
    'createdAt',
    'plotArea',
    'price',
    'distance',
];

const getSitesQueryValidator = [
    query('search').optional().isString().trim(),

    query('sortBy')
        .optional()
        .isIn(ALLOWED_SORT)
        .withMessage(`sortBy must be one of: ${ALLOWED_SORT.join(', ')}`),

    query('order')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('order must be "asc" or "desc"'),

    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('limit must be an integer between 1-100'),

    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('page must be an integer ≥ 1'),

    // ────────── address filters ──────────
    query('state').optional().isString().trim(),
    query('district').optional().isString().trim(),
    query('pincode')
        .optional()
        .isPostalCode('IN')
        .withMessage('pincode must be a valid 6-digit Indian postal code'),

    // ────────── geo filters ──────────
    query('latitude')
        .optional()
        .isFloat({ min: -90, max: 90 })
        .withMessage('latitude must be a number between -90 and 90'),

    query('longitude')
        .optional()
        .isFloat({ min: -180, max: 180 })
        .withMessage('longitude must be a number between -180 and 180'),

    query('distance')
        .optional()
        .isInt({ min: 1, max: 50000 })
        .withMessage('distance must be 1-50000 metres'),

    // ────────── custom cross-field rule ──────────
    query().custom((_, { req }) => {
        const hasLat = 'latitude' in req.query;
        const hasLng = 'longitude' in req.query;
        if (hasLat !== hasLng) {
            throw new Error('latitude and longitude must be supplied together');
        }
        return true;
    }),
];

const projectRequirementValidators = [
    body('projectType')
        .notEmpty()
        .withMessage('Project type is required')
        .isIn(['Residential', 'Commercial', 'Industrial', 'Other'])
        .withMessage('Invalid project type'),

    body('location.address')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Address is too long'),

    body('location.city')
        .optional()
        .trim()
        .isAlpha('en-US', { ignore: ' ' })
        .withMessage('City name must only contain letters'),

    body('location.state')
        .optional()
        .trim()
        .isAlpha('en-US', { ignore: ' ' })
        .withMessage('State name must only contain letters'),

    body('location.pincode')
        .optional()
        .matches(/^\d{6}$/)
        .withMessage('Pincode must be 6 digits'),

    body('location.plotSizeSqFt')
        .optional()
        .isFloat({ min: 1 })
        .withMessage('Plot size must be a positive number'),

    body('constructionType')
        .notEmpty()
        .withMessage('Construction type is required')
        .isIn(['New Construction', 'Renovation', 'Extension'])
        .withMessage('Invalid construction type'),

    body('expectedStartDate')
        .optional()
        .isISO8601()
        .withMessage('Expected start date must be in YYYY-MM-DD format'),

    body('expectedCompletionDate')
        .optional()
        .isISO8601()
        .withMessage('Expected completion date must be in YYYY-MM-DD format'),

    body('budget.minBudget')
        .optional()
        .isNumeric()
        .withMessage('Min budget must be a number'),

    body('budget.maxBudget')
        .optional()
        .isNumeric()
        .withMessage('Max budget must be a number'),

    body('designPreferences.floors')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Floors must be a non-negative integer'),

    body('designPreferences.bedrooms')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Bedrooms must be a non-negative integer'),

    body('designPreferences.bathrooms')
        .optional()
        .isInt({ min: 0 })
        .withMessage('Bathrooms must be a non-negative integer'),

    body('designPreferences.parkingRequired')
        .optional()
        .isBoolean()
        .withMessage('ParkingRequired must be a boolean'),

    body('designPreferences.gardenRequired')
        .optional()
        .isBoolean()
        .withMessage('GardenRequired must be a boolean'),

    body('designPreferences.vastuCompliance')
        .optional()
        .isBoolean()
        .withMessage('VastuCompliance must be a boolean'),

    body('additionalFacilities')
        .optional()
        .isArray()
        .withMessage('Additional facilities must be an array'),

    body('brokerAssistanceRequired')
        .optional()
        .isBoolean()
        .withMessage('Broker assistance must be a boolean'),

    body('specialInstructions')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Special instructions too long'),
];
const createServiceRequestValidators = [
    body('recipientId')
        .notEmpty()
        .withMessage('Recipient ID is required')
        .isMongoId()
        .withMessage('Recipient ID must be a valid MongoDB ObjectId'),
    body('recipientType')
        .optional()
        .isIn(['broker', 'contractor'])
        .withMessage('Recipient type must be either "broker" or "contractor"'),
];

const patchServiceRequestValidators = [
    body('status')
        .optional()
        .isIn(['accepted', 'rejected'])
        .withMessage('Status must be either "accepted" or "rejected"'),
];

const progressLogValidators = [
    body('stage')
        .trim()
        .notEmpty()
        .withMessage('Stage is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Stage must be between 2 and 100 characters'),

    body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must not exceed 1000 characters'),
];

const updateProgressLogValidators = [
    body('stage')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Stage must be between 2 and 100 characters'),

    body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description must not exceed 1000 characters'),
];

module.exports = {
    mongoIdParam,
    siteValidators,
    getSitesQueryValidator,
    projectRequirementValidators,
    createServiceRequestValidators,
    patchServiceRequestValidators,
    progressLogValidators,
    updateProgressLogValidators,
};
