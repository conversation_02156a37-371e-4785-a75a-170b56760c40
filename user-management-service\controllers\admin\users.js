const mongoose = require('mongoose');
const User = require('../../model/user');
const Contractor = require('../../model/Contractor');
const Broker = require('../../model/Broker');
const ExpressError = require('@build-connect/utils/ExpressError');

/**
 * Get all users with pagination and filtering for admin
 */
exports.getAllUsers = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        role = '',
        status = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;

        // Build query
        let query = {};
        
        // Search filter
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } }
            ];
        }

        // Role filter
        if (role) {
            query.role = role;
        }

        // Status filter (assuming isAvailable field represents active status)
        if (status === 'active') {
            query.isAvailable = true;
        } else if (status === 'inactive') {
            query.isAvailable = false;
        }

        // Sort options
        const sortOptions = {};
        sortOptions[sortBy] = order === 'desc' ? -1 : 1;

        // Execute query
        const users = await User.find(query)
            .select('-password') // Exclude password field
            .sort(sortOptions)
            .skip(skip)
            .limit(limitNum)
            .lean();

        // Get total count for pagination
        const totalCount = await User.countDocuments(query);
        const totalPages = Math.ceil(totalCount / limitNum);

        res.status(200).json({
            users,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalCount,
                limit: limitNum,
                hasNextPage: pageNum < totalPages,
                hasPrevPage: pageNum > 1
            }
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch users', 500);
    }
};

/**
 * Get user by ID for admin
 */
exports.getUserById = async (req, res) => {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid user ID', 400);
    }

    try {
        const user = await User.findById(id).select('-password').lean();
        
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        // Get additional profile data based on role
        let profileData = null;
        if (user.role === 'contractor') {
            profileData = await Contractor.findOne({ user: id }).lean();
        } else if (user.role === 'broker') {
            profileData = await Broker.findOne({ user: id }).lean();
        }

        res.status(200).json({
            user: {
                ...user,
                profileData
            }
        });
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Failed to fetch user', 500);
    }
};

/**
 * Update user status (activate/deactivate)
 */
exports.updateUserStatus = async (req, res) => {
    const { id } = req.params;
    const { isActive, reason } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid user ID', 400);
    }

    try {
        const user = await User.findById(id);
        
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        // Update user status
        user.isAvailable = isActive;
        await user.save();

        res.status(200).json({
            message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
            user: {
                id: user._id,
                name: user.name,
                email: user.email,
                isAvailable: user.isAvailable
            },
            reason
        });
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Failed to update user status', 500);
    }
};

/**
 * Get user statistics for admin dashboard
 */
exports.getUserStats = async (req, res) => {
    const { timeframe = '30d' } = req.query;

    try {
        // Calculate date range based on timeframe
        let dateFilter = {};
        const now = new Date();
        
        switch (timeframe) {
            case '7d':
                dateFilter.createdAt = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
                break;
            case '30d':
                dateFilter.createdAt = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
                break;
            case '90d':
                dateFilter.createdAt = { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) };
                break;
            case 'all':
            default:
                // No date filter for 'all'
                break;
        }

        // Get basic user statistics
        const [
            totalUsers,
            activeUsers,
            inactiveUsers,
            newUsers,
            contractors,
            brokers,
            regularUsers,
            verifiedUsers,
            unverifiedUsers
        ] = await Promise.all([
            User.countDocuments(),
            User.countDocuments({ isAvailable: true }),
            User.countDocuments({ isAvailable: false }),
            User.countDocuments(dateFilter),
            User.countDocuments({ role: 'contractor' }),
            User.countDocuments({ role: 'broker' }),
            User.countDocuments({ role: 'user' }),
            User.countDocuments({ 
                $or: [
                    { isEmailVerified: true },
                    { isPhoneVerified: true }
                ]
            }),
            User.countDocuments({ 
                isEmailVerified: false,
                isPhoneVerified: false
            })
        ]);

        res.status(200).json({
            timeframe,
            stats: {
                totalUsers,
                activeUsers,
                inactiveUsers,
                newUsers,
                contractors,
                brokers,
                regularUsers,
                verifiedUsers,
                unverifiedUsers
            }
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch user statistics', 500);
    }
};

/**
 * Get contractors with verification status for admin
 */
exports.getContractors = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        verificationStatus = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;

        // Build aggregation pipeline
        const pipeline = [
            {
                $lookup: {
                    from: 'users',
                    localField: 'user',
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $unwind: '$userInfo'
            }
        ];

        // Add search filter
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { 'userInfo.name': { $regex: search, $options: 'i' } },
                        { 'userInfo.email': { $regex: search, $options: 'i' } },
                        { 'userInfo.phone': { $regex: search, $options: 'i' } }
                    ]
                }
            });
        }

        // Add verification status filter
        if (verificationStatus) {
            pipeline.push({
                $match: { verificationStatus }
            });
        }

        // Add sorting
        const sortOptions = {};
        sortOptions[sortBy] = order === 'desc' ? -1 : 1;
        pipeline.push({ $sort: sortOptions });

        // Execute aggregation with pagination
        const contractors = await Contractor.aggregate([
            ...pipeline,
            { $skip: skip },
            { $limit: limitNum },
            {
                $project: {
                    _id: 1,
                    serviceAreas: 1,
                    specialties: 1,
                    experience: 1,
                    ratings: 1,
                    verificationStatus: 1,
                    verifiedBy: 1,
                    approvalDate: 1,
                    reasonForRejection: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    'userInfo.name': 1,
                    'userInfo.email': 1,
                    'userInfo.phone': 1,
                    'userInfo.location': 1,
                    'userInfo.isAvailable': 1
                }
            }
        ]);

        // Get total count
        const totalCountPipeline = [...pipeline, { $count: 'total' }];
        const countResult = await Contractor.aggregate(totalCountPipeline);
        const totalCount = countResult.length > 0 ? countResult[0].total : 0;
        const totalPages = Math.ceil(totalCount / limitNum);

        res.status(200).json({
            contractors,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalCount,
                limit: limitNum,
                hasNextPage: pageNum < totalPages,
                hasPrevPage: pageNum > 1
            }
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch contractors', 500);
    }
};

/**
 * Get brokers with verification status for admin
 */
exports.getBrokers = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        verificationStatus = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;

        // Build aggregation pipeline
        const pipeline = [
            {
                $lookup: {
                    from: 'users',
                    localField: 'user',
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $unwind: '$userInfo'
            }
        ];

        // Add search filter
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { 'userInfo.name': { $regex: search, $options: 'i' } },
                        { 'userInfo.email': { $regex: search, $options: 'i' } },
                        { 'userInfo.phone': { $regex: search, $options: 'i' } }
                    ]
                }
            });
        }

        // Add verification status filter
        if (verificationStatus) {
            pipeline.push({
                $match: { verificationStatus }
            });
        }

        // Add sorting
        const sortOptions = {};
        sortOptions[sortBy] = order === 'desc' ? -1 : 1;
        pipeline.push({ $sort: sortOptions });

        // Execute aggregation with pagination
        const brokers = await Broker.aggregate([
            ...pipeline,
            { $skip: skip },
            { $limit: limitNum },
            {
                $project: {
                    _id: 1,
                    serviceAreas: 1,
                    experience: 1,
                    ratings: 1,
                    verificationStatus: 1,
                    verifiedBy: 1,
                    approvalDate: 1,
                    reasonForRejection: 1,
                    createdAt: 1,
                    updatedAt: 1,
                    'userInfo.name': 1,
                    'userInfo.email': 1,
                    'userInfo.phone': 1,
                    'userInfo.location': 1,
                    'userInfo.isAvailable': 1
                }
            }
        ]);

        // Get total count
        const totalCountPipeline = [...pipeline, { $count: 'total' }];
        const countResult = await Broker.aggregate(totalCountPipeline);
        const totalCount = countResult.length > 0 ? countResult[0].total : 0;
        const totalPages = Math.ceil(totalCount / limitNum);

        res.status(200).json({
            brokers,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalCount,
                limit: limitNum,
                hasNextPage: pageNum < totalPages,
                hasPrevPage: pageNum > 1
            }
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch brokers', 500);
    }
};
