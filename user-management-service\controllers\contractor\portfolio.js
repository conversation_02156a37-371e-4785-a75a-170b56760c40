const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const Contractor = require('../../model/Contractor');
const { Cloudinary } = require('../../cloudinary');

exports.addPortfolioItem = async (req, res) => {
    const userId = req.user.id;
    const { caption } = req.body;

    if (!req.file) {
        throw new ExpressError('Portfolio image is required', 400);
    }

    if (!caption) {
        throw new ExpressError('Caption is required', 400);
    }

    const result = await withTransaction(async (session) => {
        // Find contractor profile
        const contractor = await Contractor.findOne({ user: userId }).session(
            session
        );
        if (!contractor) {
            throw new ExpressError('Contractor profile not found', 404);
        }

        // Create portfolio item
        const portfolioItem = {
            image: req.file.path,
            caption: caption.trim(),
            createdAt: new Date(),
        };

        // Add to portfolio array
        contractor.portfolio.push(portfolioItem);
        await contractor.save({ session });

        return portfolioItem;
    });

    res.status(201).json({
        success: true,
        message: 'Portfolio item added successfully',
        portfolioItem: result,
    });
};

exports.updatePortfolioItem = async (req, res) => {
    const userId = req.user.id;
    const { portfolioItemId } = req.params;
    const { caption } = req.body;

    const result = await withTransaction(async (session) => {
        const contractor = await Contractor.findOne({
            user: userId,
        }).session(session);
        if (!contractor) {
            throw new ExpressError('Contractor profile not found', 404);
        }

        const portfolioItem = contractor.portfolio.id(portfolioItemId);
        if (!portfolioItem) {
            throw new ExpressError('Portfolio item not found', 404);
        }

        // Update image if provided
        if (req.file) {
            // Delete old image from Cloudinary
            const oldImageUrl = portfolioItem.image;
            if (oldImageUrl) {
                const publicId = oldImageUrl.split('/').pop().split('.')[0];
                await Cloudinary.destroy(publicId);
            }
            portfolioItem.image = req.file.path;
        }

        // Update caption if provided
        if (caption) {
            portfolioItem.caption = caption.trim();
        }

        await contractor.save({ session });
        return portfolioItem;
    });

    res.status(200).json({
        success: true,
        message: 'Portfolio item updated successfully',
        portfolioItem: result,
    });
};

exports.deletePortfolioItem = async (req, res) => {
    const userId = req.user.id;
    const { portfolioItemId } = req.params;

    await withTransaction(async (session) => {
        const contractor = await Contractor.findOne({
            user: userId,
        }).session(session);
        if (!contractor) {
            throw new ExpressError('Contractor profile not found', 404);
        }

        const portfolioItem = contractor.portfolio.id(portfolioItemId);
        if (!portfolioItem) {
            throw new ExpressError('Portfolio item not found', 404);
        }

        // Delete image from Cloudinary
        const imageUrl = portfolioItem.image;
        if (imageUrl) {
            const publicId = imageUrl.split('/').pop().split('.')[0];
            await Cloudinary.destroy(publicId);
        }

        // Remove from portfolio array
        contractor.portfolio.pull(portfolioItemId);
        await contractor.save({ session });
    });

    res.status(200).json({
        success: true,
        message: 'Portfolio item deleted successfully',
    });
};

exports.getPortfolioItems = async (req, res) => {
    const { contractorId } = req.params;

    const contractor = await Contractor.findById(contractorId).lean();
    if (!contractor) {
        throw new ExpressError('Contractor not found', 404);
    }

    res.status(200).json({
        success: true,
        portfolio: contractor.portfolio,
    });
};
