const Broker = require('../../model/Broker');
const Contractor = require('../../model/Contractor');
const User = require('../../model/user');
const Asset = require('../../model/Asset');

exports.getBrokers = async (req, res) => {
    const { serviceArea } = req.query;
    
    // Build filter for verified brokers
    const filter = { verificationStatus: 'verified' };
    if (serviceArea) {
        filter.serviceAreas = { $in: [serviceArea] };
    }

    const brokers = await Broker.find(filter)
        .populate('user', 'name email location')
        .lean();

    // Get avatars for all brokers
    const brokersWithAvatars = await Promise.all(
        brokers.map(async (broker) => {
            const avatar = await Asset.findOne({
                entityId: broker.user._id,
                entityType: 'User',
                assetType: 'avatar',
            }).lean();

            return {
                id: broker.user._id,
                name: broker.user.name,
                email: broker.user.email,
                location: broker.user.location,
                serviceAreas: broker.serviceAreas,
                experience: broker.experience,
                ratings: broker.ratings,
                avatar: avatar ? avatar.imageURL : null,
            };
        })
    );

    res.status(200).json({ brokers: brokersWithAvatars });
};

exports.getContractors = async (req, res) => {
    const { serviceArea, specialty } = req.query;
    
    // Build filter for verified contractors
    const filter = { verificationStatus: 'verified' };
    if (serviceArea) {
        filter.serviceAreas = { $in: [serviceArea] };
    }
    if (specialty) {
        filter.specialties = { $in: [specialty] };
    }

    const contractors = await Contractor.find(filter)
        .populate('user', 'name email location')
        .lean();

    // Get avatars for all contractors
    const contractorsWithAvatars = await Promise.all(
        contractors.map(async (contractor) => {
            const avatar = await Asset.findOne({
                entityId: contractor.user._id,
                entityType: 'User',
                assetType: 'avatar',
            }).lean();

            return {
                id: contractor.user._id,
                name: contractor.user.name,
                email: contractor.user.email,
                location: contractor.user.location,
                serviceAreas: contractor.serviceAreas,
                specialties: contractor.specialties,
                experience: contractor.experience,
                ratings: contractor.ratings,
                avatar: avatar ? avatar.imageURL : null,
            };
        })
    );

    res.status(200).json({ contractors: contractorsWithAvatars });
};
