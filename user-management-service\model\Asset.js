const mongoose = require('mongoose');

const assetSchema = new mongoose.Schema(
    {
        imageURL: {
            type: String,
            required: true,
            trim: true,
        },
        entityId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            refPath: 'entityType',
        },
        entityType: {
            type: String,
            required: true,
            enum: [
                'User',
                'ContractorProfile',
                'BrokerProfile',
                'Aadhaar',
                'PAN',
            ],
        },
        assetType: {
            type: String,
            required: true,
            enum: ['aadhar', 'pan', 'avatar', 'portfolio'],
        },
        fileType: {
            type: String,
            required: true,
            enum: ['png', 'jpeg', 'jpg', 'pdf'],
        },
        fileName: {
            type: String,
            required: true,
            trim: true,
        },
    },
    {
        timestamps: true,
    }
);

const Asset = mongoose.model('Asset', assetSchema);
module.exports = Asset;
