const mongoose = require('mongoose');

const brokerSchema = new mongoose.Schema(
    {
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            unique: true,
        },
        experience: {
            type: Number,
            default: 0,
        },
        serviceAreas: {
            type: [String],
            default: [],
        },
        ratings: {
            type: Number,
            default: 0,
        },
        verificationStatus: {
            type: String,
            enum: ['pending', 'verified', 'rejected'],
            default: 'pending',
        },
        verifiedBy: {
            type: mongoose.Schema.Types.ObjectId,
        },
        approvalDate: {
            type: Date,
        },
        reasonForRejection: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: true,
    }
);

const Broker = mongoose.model('Broker', brokerSchema);
module.exports = Broker;
